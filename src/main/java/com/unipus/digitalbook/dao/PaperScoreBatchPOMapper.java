package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.paper.PaperScoreBatchPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;

import java.util.List;

/**
 * 成绩提交批次表Mapper接口
 */
@Mapper
public interface PaperScoreBatchPOMapper {

    int insertOrUpdate(PaperScoreBatchPO record);

    int updateById(PaperScoreBatchPO record);

    List<PaperScoreBatchPO> selectByCondition(PaperScoreBatchPO condition);

    PaperScoreBatchPO getLatestPaperScoreBatch(PaperScoreBatchPO condition);

    Integer getPaperScoreBatchStatus(String scoreBatchId);

    PaperScoreBatchPO gePaperScoreBatchByInstanceId(
            @Param("instanceId") String instanceId,
            @Param("status") @Nullable Integer status);

    int deletePreviewInfo(
            @Param("paperId") String paperId,
            @Param("versionNumber") String versionNumber,
            @Param("openId") String openId,
            @Param("tenantId") Long tenantId
    );
}
