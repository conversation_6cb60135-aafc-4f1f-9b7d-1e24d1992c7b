package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.JSON;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.*;
import com.qcloud.cos.model.ciModel.job.*;
import com.qcloud.cos.model.ciModel.job.v2.MediaJobResponseV2;
import com.qcloud.cos.model.ciModel.job.v2.MediaJobsRequestV2;
import com.qcloud.cos.model.ciModel.mediaInfo.*;
import com.qcloud.cos.model.ciModel.template.MediaListTemplateResponse;
import com.qcloud.cos.model.ciModel.template.MediaTemplateObject;
import com.qcloud.cos.model.ciModel.template.MediaTemplateRequest;
import com.qcloud.cos.model.ciModel.template.MediaTemplateResponse;
import com.qcloud.cos.utils.IOUtils;
import com.qcloud.cos.utils.Jackson;
import com.qcloud.cos.utils.Md5Utils;
import com.qcloud.cos.utils.StringUtils;
import com.tencent.cloud.CosStsClient;
import com.tencent.cloud.Policy;
import com.tencent.cloud.Response;
import com.tencent.cloud.Statement;
import com.unipus.digitalbook.common.exception.cos.COSCredentialException;
import com.unipus.digitalbook.common.exception.cos.COSException;
import com.unipus.digitalbook.common.utils.BloomFilterUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.conf.cos.COSProperties;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.constants.CommonConstant;
import com.unipus.digitalbook.model.entity.cos.COSCredential;
import com.unipus.digitalbook.model.entity.cos.COSMediaTranscodeJob;
import com.unipus.digitalbook.model.entity.cos.MediaTranscodeTemplate;
import com.unipus.digitalbook.model.enums.MediaTranscodeFormatEnum;
import com.unipus.digitalbook.model.enums.MediaTranscodeTemplateEnum;
import com.unipus.digitalbook.service.COSService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * COS业务服务实现类
 *
 * <AUTHOR>
 * @date 2024/12/11
 */
@Service
@Slf4j
public class COSServiceImpl implements COSService {

    @Resource
    private COSProperties cosProperties;

    @Resource
    private COSClient cosClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private BloomFilterUtil bloomFilterUtil;

    /**
     * 获取当前客户端配置中的region
     *
     * @return region
     */
    @Override
    public String getRegion() {
        return cosProperties.getRegion();
    }

    /**
     * 获取当前客户端配置中的bucket
     *
     * @return bucket
     */
    @Override
    public String getBucket() {
        return cosProperties.getBucket();
    }


    /**
     * 获取当前客户端配置中的appid
     *
     * @return 返回当前凭证中的appid
     */
    @Override
    public String getAppId() {
        // 检查bucket名称是否符合 "bucketname-appid" 格式
        if (cosProperties.getBucket().contains("-")) {
            String[] parts = cosProperties.getBucket().split("-");
            return parts[parts.length - 1];
        }
        return null;
    }

    /**
     * 获取当前客户端配置中的域名
     *
     * @return 域名
     */
    @Override
    public String getDomain() {
        return String.format("%s://%s/",
                cosClient.getClientConfig().getHttpProtocol(),
                cosClient.getClientConfig().getEndpointBuilder().buildGeneralApiEndpoint(cosProperties.getBucket()));
    }

    /**
     * 获取COS（Cloud Object Storage）的临时凭证
     * 该方法用于生成一个具有临时权限的COS凭证，用于访问腾讯云对象存储服务
     * 它根据配置的密钥信息、桶信息、区域信息以及定义的权限策略来获取临时凭证
     *
     * @param actions 权限操作列表
     * @return COSCredential 实体对象，包含访问COS所需的临时凭证信息
     * @throws IllegalArgumentException 如果提供的密钥信息无效，抛出此异常
     */
    @Override
    public COSCredential getCredential(String[] actions, String[] resources) {
        TreeMap<String, Object> config = new TreeMap<>();
        config.put("secretId", cosProperties.getSecretId());
        config.put("secretKey", cosProperties.getSecretKey());
        // 设置域名:如果您使用了腾讯云 cvm，可以设置内部域名
        //config.put("host", "sts.internal.tencentcloudapi.com");
        // 临时密钥有效时长，单位是秒，默认 1800 秒，目前主账号最长 2 小时（即 7200 秒），子账号最长 36 小时（即 129600）秒
        config.put("durationSeconds", 1800);
        config.put("bucket", cosProperties.getBucket());
        config.put("region", cosProperties.getRegion());

        // 开始构建一条 statement
        Statement statement = new Statement();
        // 声明设置的结果是允许操作
        statement.setEffect("allow");
        /**
         * 密钥的权限列表。必须在这里指定本次临时密钥所需要的权限。
         * 权限列表请参见 https://cloud.tencent.com/document/product/436/31923
         * 规则为 {project}:{interfaceName}
         * project : 产品缩写  cos相关授权为值为cos,数据万象(数据处理)相关授权值为ci
         * 授权所有接口用*表示，例如 cos:*,ci:*
         * 添加一批操作权限 :
         */
        statement.addActions(actions);
        /**
         * 这里改成允许的路径前缀，可以根据自己网站的用户登录态判断允许上传的具体路径
         * 资源表达式规则分对象存储(cos)和数据万象(ci)两种
         * 数据处理、审核相关接口需要授予ci资源权限
         *  cos : qcs::cos:{region}:uid/{appid}:{bucket}/{path}
         *  ci  : qcs::ci:{region}:uid/{appid}:bucket/{bucket}/{path}
         * 列举几种典型的{path}授权场景：
         * 1、允许访问所有对象："*"
         * 2、允许访问指定的对象："a/a1.txt", "b/b1.txt"
         * 3、允许访问指定前缀的对象："a*", "a/*", "b/*"
         *  如果填写了“*”，将允许用户访问所有资源；除非业务需要，否则请按照最小权限原则授予用户相应的访问权限范围。
         *
         * 示例：授权examplebucket-1250000000 bucket目录下的所有资源给cos和ci 授权两条Resource
         */
        statement.addResources(ArrayUtils.isNotEmpty(resources) ? resources : new String[]{"*"});

        // 把一条 statement 添加到 policy
        Policy policy = new Policy();
        policy.addStatement(statement);
        // 将 Policy 示例转化成 String，可以使用任何 json 转化方式，这里是本 SDK 自带的推荐方式
        config.put("policy", Jackson.toJsonPrettyString(policy));
        try {
            Response credential = CosStsClient.getCredential(config);
            return new COSCredential(
                    credential.expiration,
                    credential.startTime,
                    credential.expiredTime,
                    credential.credentials.tmpSecretId,
                    credential.credentials.tmpSecretKey,
                    credential.credentials.sessionToken
            );
        } catch (IOException e) {
            log.error("获取COS临时凭证异常", e);
            throw new COSCredentialException();
        }
    }

    /**
     * 根据指定的键获取对象的URL
     *
     * @param key 对象的唯一标识符
     * @return 对象的URL地址
     */
    @Override
    public String getUrlByKey(String key) {
        return URLDecoder.decode(cosClient.getObjectUrl(cosProperties.getBucket(), key).toString(), StandardCharsets.UTF_8);
    }

    /**
     * 根据URL获取对象键
     *
     * @param url 对象的URL地址
     * @return 对象的键，即对象在存储桶中的唯一标识符
     */
    @Override
    public String getKeyByUrl(String url) {
        if (StringUtils.isNullOrEmpty(url)) {
            throw new IllegalArgumentException("URL不能为空");
        }
        // 构建API端点URL
        String apiEndpoint = String.format("%s://%s/",
                cosClient.getClientConfig().getHttpProtocol(),
                cosClient.getClientConfig().getEndpointBuilder().buildGeneralApiEndpoint(cosProperties.getBucket()));
        // 判断是完整URL还是对象键
        String objectKey = url.contains(apiEndpoint) ? url.substring(apiEndpoint.length()) : url;
        log.debug("对象标识符:{} --- {}", url, objectKey);
        return objectKey;
    }

    /**
     * 获取预签名URL
     *
     * @param key            对象的键，即对象在存储桶中的唯一标识符
     * @param expirationDate 签名的过期时间，指定到具体的日期和时间点
     * @return 返回生成的预签名URL字符串
     */
    @Override
    public String getPresignedUrlByKey(String key, Date expirationDate) {
        // 设置签名过期时间(可选), 若未进行设置, 则默认使用 ClientConfig 中的签名过期时间(1小时)
        return cosClient.generatePresignedUrl(cosProperties.getBucket(), key, expirationDate).toString();
    }

    /**
     * 获取预签名URL
     *
     * @param url            文件URL
     * @param expirationDate 过期时间
     * @return 预签名URL
     */
    @Override
    public String getPresignedUrlByUrl(String url, Date expirationDate) {
        log.info("获取预签名URL，文件URL: {}", url);
        String objectKey = getKeyByUrl(url);
        return getPresignedUrlByKey(objectKey, expirationDate == null ? new Date(System.currentTimeMillis() + 60 * 1000) : expirationDate);
    }

    /**
     * 获取预签名URL
     *
     * @param url 原始URL
     * @return 预签名URL
     */
    @Override
    public String getCachePresignedUrlByUrl(String url) {
        if (org.apache.commons.lang3.StringUtils.isBlank(url)) {
            return url;
        }

        // 构建缓存键：使用前缀 + URL的MD5哈希值作为唯一标识
        String cacheKey = CacheConstant.REDIS_COS_PRESIGNED_URL_PREFIX + DigestUtils.md5Hex(url);

        // 先从缓存中获取预签名URL
        String cachedPresignedUrl = stringRedisTemplate.opsForValue().get(cacheKey);
        if (cachedPresignedUrl != null) {
            log.debug("从缓存中获取预签名URL: {} , cacheKey: {}", url, cacheKey);
            return cachedPresignedUrl;
        }

        // 缓存中没有，调用COSUtil生成新的预签名URL
        String presignedUrl = getPresignedUrlByUrl(url, new Date(System.currentTimeMillis() + 60 * 1000));

        // 将预签名URL存入缓存，过期时间比实际URL过期时间短10秒
        stringRedisTemplate.opsForValue().set(cacheKey, presignedUrl,
                CacheConstant.REDIS_COS_PRESIGNED_URL_TIMEOUT_SECONDS, TimeUnit.SECONDS);

        log.debug("生成并缓存预签名URL: {} , cacheKey: {}", url, cacheKey);
        return presignedUrl;
    }

    /**
     * 上传文件并获取其URL地址
     *
     * @param fileName 文件名，用于指定要上传的文件名称
     * @param bytes    文件内容的字节数组，包含要上传的文件的数据
     * @return 返回上传文件的URL地址，通过这个URL可以访问到上传的文件
     */
    @Override
    public String getUploadUrl(String fileName, byte[] bytes) {
        uploadFile(fileName, bytes);
        return getUrlByKey(fileName);
    }

    /**
     * 上传文件并获取其URL地址
     *
     * @param fileName 文件名，用于指定要上传的文件名称
     * @param content  文件内容的字符串
     * @return 返回上传文件的URL地址，通过这个URL可以访问到上传的文件
     */
    @Override
    public String getUploadContentUrl(String fileName, String content) {
        uploadContent(fileName, content, false);
        return getUrlByKey(fileName);
    }

    /**
     * 上传文件并获取其URL地址
     *
     * @param fileName 文件名，用于指定要上传的文件名称
     * @param content  文件内容的字符串
     * @return 返回上传文件的URL地址，通过这个URL可以访问到上传的文件
     */
    @Override
    public String getPrivateUploadContentUrl(String fileName, String content) {
        uploadContent(fileName, content, true);
        return getUrlByKey(fileName);
    }

    /**
     * 上传文件并获取预签名URL
     *
     * @param fileName       文件名，用于标识要上传的文件
     * @param bytes          文件的字节流，包含文件的具体内容
     * @param expirationDate 预签名URL的过期时间，超过这个时间URL将无法访问文件
     * @return 返回一个预签名URL，通过这个URL可以在有效期内访问上传的文件
     */
    @Override
    public String getUploadPresignedUrl(String fileName, byte[] bytes, Date expirationDate) {
        uploadFile(fileName, bytes);
        return getPresignedUrlByKey(fileName, expirationDate);
    }

    /**
     * 上传文件到COS存储桶
     *
     * @param fileName 文件名，用于在存储桶中标识文件
     * @param bytes    文件内容的字节数组
     * @return 返回上传后的文件对象信息
     * @throws COSException 如果文件上传过程中发生错误，则抛出此异常
     */
    private PutObjectResult uploadFile(String fileName, byte[] bytes) {
        try (InputStream is = new ByteArrayInputStream(bytes)) {
            //获取文件的类型
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(bytes.length);
            PutObjectRequest putObjectRequest = new PutObjectRequest(cosProperties.getBucket(), fileName, is, objectMetadata);
            return cosClient.putObject(putObjectRequest);
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new COSException("文件上传失败");
        }
    }

    /**
     * 上传文本内容到COS存储桶
     *
     * @param fileName  文件名，用于在存储桶中标识文件
     * @param content   文件内容的字符串
     * @param isPrivate 是否为私有文件
     * @return 返回上传后的文件对象信息
     * @throws COSException 如果文件上传过程中发生错误，则抛出此异常
     */
    private PutObjectResult uploadContent(String fileName, String content, boolean isPrivate) {
        byte[] contentByteArray = content.getBytes(StringUtils.UTF8);
        String contentMd5 = Md5Utils.md5AsBase64(contentByteArray);
        InputStream contentInput = new ByteArrayInputStream(contentByteArray);
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(contentByteArray.length);
        objectMetadata.setContentMD5(contentMd5);
        objectMetadata.setContentDisposition("inline");
        objectMetadata.setCacheControl("no-cache");
        PutObjectRequest putObjectRequest = new PutObjectRequest(cosProperties.getBucket(), fileName, contentInput, objectMetadata);
        if (isPrivate) {
            putObjectRequest.withCannedAcl(CannedAccessControlList.Private);
        }
        return cosClient.putObject(putObjectRequest);
    }

    /**
     * 上传文件到腾讯云COS服务
     *
     * @param originalFilename 原始文件名，用于获取文件类型
     * @param fileSize         文件大小，用于设置ObjectMetadata
     * @param inputStream      文件输入流，用于上传文件
     * @return 返回上传文件的URL地址
     */
    private String uploadFile(String originalFilename, Long fileSize, InputStream inputStream) {
        try (InputStream is = inputStream) {
            //获取文件的类型
            String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
            //使用UUID工具  创建唯一名称，放置文件重名被覆盖，在拼接上上命令获取的文件类型
            //int pos = originalFilename.lastIndexOf(".");
            //originalFilename.substring(0, pos) + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + originalFilename.substring(pos);
            String fileName = UUID.randomUUID() + fileType;
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(fileSize);
            PutObjectRequest putObjectRequest = new PutObjectRequest(cosProperties.getBucket(), fileName, is, objectMetadata);
            PutObjectResult putResult = cosClient.putObject(putObjectRequest);
            log.info("文件上传成功，文件名: {}, ETag: {}", fileName, putResult.getETag());
            return cosClient.getObjectUrl(cosProperties.getBucket(), fileName).toString();
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new COSException("文件上传失败");
        }
    }

    /**
     * 根据URL获取文件内容
     *
     * @param url 对象的URL地址
     * @return 文件内容
     */
    @Override
    public String getDownloadContentByUrl(String url) {
        String objectKey = getKeyByUrl(url);
        GetObjectRequest getObjectRequest = new GetObjectRequest(cosProperties.getBucket(), objectKey);
        try {
            COSObject cosObject = cosClient.getObject(getObjectRequest);
            try (InputStream cosObjectInput = cosObject.getObjectContent()) {
                return IOUtils.toString(cosObjectInput);
            }
        } catch (IOException e) {
            log.error("读取COS文件内容失败，URL: {}", url, e);
            throw new COSException("读取文件失败");
        }
    }

    /**
     * 根据URL下载文件内容（带缓存）
     *
     * @param contentUrl     内容URL
     * @param cachePrefix    缓存前缀
     * @param timeoutSeconds 缓存超时时间（秒）
     * @return 内容字符串
     */
    @Override
    public String getCacheDownloadContentByUrl(String contentUrl, String cachePrefix, long timeoutSeconds) {
        if (org.apache.commons.lang3.StringUtils.isBlank(contentUrl)) {
            return null;
        }

        // 构建缓存键：使用前缀 + URL的MD5哈希值作为唯一标识
        String cacheKey = cachePrefix + DigestUtils.md5Hex(contentUrl);

        // 先从缓存中获取内容
        String content = stringRedisTemplate.opsForValue().get(cacheKey);
        if (content != null) {
            log.debug("从缓存中获取内容: {} , cacheKey: {}", contentUrl, cacheKey);
            return content;
        }

        // 缓存中没有，调用COSUtil下载内容
        content = getDownloadContentByUrl(contentUrl);

        // 如果成功获取到内容，将其存入缓存
        if (content != null) {
            stringRedisTemplate.opsForValue().set(cacheKey, content, timeoutSeconds, TimeUnit.SECONDS);
            log.debug("下载并缓存内容: {} , cacheKey: {}", contentUrl, cacheKey);
        }

        return content;
    }

    /**
     * 复制COS对象到新的路径
     *
     * @param sourceUrl 源文件URL
     * @param targetUrl 目标文件URL
     * @return 目标文件的完整URL
     */
    @Override
    public String copyObject(String sourceUrl, String targetUrl) {
        if (StringUtils.isNullOrEmpty(sourceUrl) || StringUtils.isNullOrEmpty(targetUrl)) {
            throw new IllegalArgumentException("源URL和目标URL不能为空");
        }
        try {
            String sourceKey = getKeyByUrl(sourceUrl);
            String targetKey = getKeyByUrl(targetUrl);
            log.debug("开始复制COS对象，源键: {}, 目标键: {}", sourceKey, targetKey);
            // 构建复制源
            CopyObjectRequest copyObjectRequest = new CopyObjectRequest(
                    cosProperties.getBucket(), // 源存储桶
                    sourceKey,                  // 源对象键
                    cosProperties.getBucket(),  // 目标存储桶
                    targetKey                   // 目标对象键
            );
            // 执行复制操作
            CopyObjectResult copyResult = cosClient.copyObject(copyObjectRequest);
            log.info("COS对象复制成功，源: {}, 目标: {}, ETag: {}", sourceKey, targetKey, copyResult.getETag());
            // 返回目标文件的完整URL
            return getUrlByKey(targetKey);
        } catch (Exception e) {
            log.error("复制COS对象失败，源URL: {}, 目标文件URL: {}", sourceUrl, targetUrl, e);
            throw new COSException("文件复制失败");
        }
    }

    /**
     * 创建音视频转码模板
     *
     * @param templateName 模板名称
     * @return 创建成功的模板ID
     * @throws COSException 如果创建模板失败
     */
    private String createMediaTranscodeTemplate(String templateName) {
        try {
            // 根据模板名称获取枚举配置
            MediaTranscodeTemplateEnum templateEnum = MediaTranscodeTemplateEnum.getByTemplateName(templateName);
            if (templateEnum == null) {
                throw new IllegalArgumentException("不支持的模板类型: " + templateName);
            }
            MediaTemplateRequest request = new MediaTemplateRequest();
            request.setBucketName(cosProperties.getBucket());
            request.setTag("Transcode");
            request.setName(templateName);

            // 配置转码参数 - 根据模板名称配置不同的转码参数
            if (MediaTranscodeTemplateEnum.AUDIO_96KHZ.getTemplateName().equals(templateName)) {
                request.getContainer().setFormat("mp4");
                request.getContainer().setClipConfig(new MediaClipConfig());
                MediaAudioObject audio = request.getAudio();
                audio.setCodec("aac");
                audio.setSamplerate("96000");
            }

            MediaTemplateResponse response = cosClient.createMediaTemplate(request);
            String templateId = response.getTemplate().getTemplateId();
            log.info("创建转码模板成功，模板名称: {}, 模板ID: {}", templateName, templateId);
            return templateId;
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建转码模板失败，模板名称: {}", templateName, e);
            throw new COSException("创建转码模板失败");
        }
    }

    /**
     * 查询音视频转码模板
     *
     * @param name 模板名称
     * @return 模板ID
     */
    @Override
    public String getMediaTranscodeTemplate(String name) {
        try {
            log.info("查询音视频转码模板，模板名称: {}", name);
            MediaTemplateRequest request = new MediaTemplateRequest();
            request.setBucketName(cosProperties.getBucket());
            request.setTag("Transcode");
            request.setName(name);
            request.setPageNumber("1");
            request.setPageSize("1");
            MediaListTemplateResponse response = cosClient.describeMediaTemplates(request);
            if (CollectionUtils.isEmpty(response.getTemplateList())) {
                return null;
            }
            MediaTemplateObject mediaTemplateObject = response.getTemplateList().getFirst();
            return mediaTemplateObject.getTemplateId();
        } catch (Exception e) {
            log.error("查询音视频转码模板失败，模板名称: {}", name, e);
            throw new COSException("查询音视频转码模板失败");
        }
    }

    /**
     * 查询音视频转码模板（带缓存）
     *
     * @param name 模板名称
     * @return 模板ID，如果不存在则创建新模板
     */
    @Override
    public String getCachedMediaTranscodeTemplate(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("模板名称不能为空");
        }
        // 构建缓存键
        String cacheKey = CacheConstant.REDIS_COS_MEDIA_TRANSCODE_PREFIX + name;
        // 先从缓存中获取
        String templateId = stringRedisTemplate.opsForValue().get(cacheKey);
        if (templateId != null) {
            log.debug("从缓存中获取转码模板ID，模板名称: {}, 模板ID: {}", name, templateId);
            return templateId;
        }
        // 缓存中没有，调用原方法查询查询现有模板
        templateId = getMediaTranscodeTemplate(name);
        if (templateId == null) {
            // 模板不存在则创建
            log.info("模板不存在，创建新模板，模板名称: {}", name);
            templateId = createMediaTranscodeTemplate(name);
        }
        // 缓存模板ID
        if (templateId != null) {
            stringRedisTemplate.opsForValue().set(cacheKey, templateId,
                    CacheConstant.REDIS_COS_MEDIA_TEMPLATE_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            log.debug("模板已缓存，模板名称: {}, 模板ID: {}", name, templateId);
        }
        return templateId;
    }

    /**
     * 查询所有音视频转码模板
     *
     * @return 模板列表
     * @throws COSException 如果查询模板失败
     */
    @Override
    public List<MediaTranscodeTemplate> getAllMediaTranscodeTemplates() {
        try {
            log.info("查询所有音视频转码模板");
            MediaTemplateRequest request = new MediaTemplateRequest();
            request.setBucketName(cosProperties.getBucket());
            request.setTag("Transcode");
            request.setPageNumber("1");
            request.setPageSize("100"); // 设置较大的页面大小以获取所有模板
            MediaListTemplateResponse response = cosClient.describeMediaTemplates(request);
            if (CollectionUtils.isEmpty(response.getTemplateList())) {
                return Collections.emptyList();
            }
            log.info("查询到音视频转码模板数量: {}", response.getTemplateList().size());
            return response.getTemplateList().stream()
                    .map(MediaTranscodeTemplate::new)
                    .toList();
        } catch (Exception e) {
            log.error("查询所有音视频转码模板失败", e);
            throw new COSException("查询所有音视频转码模板失败");
        }
    }

    /**
     * 删除音视频转码模板
     *
     * @param templateId 模板ID
     * @return 删除是否成功
     * @throws COSException 如果删除模板失败
     */
    @Override
    public Boolean deleteMediaTranscodeTemplate(String templateId) {
        try {
            log.info("删除媒体处理模板，模板ID: {}", templateId);
            // 先查询模板名称，用于清除缓存
            String templateName = getMediaTranscodeTemplateNameById(templateId);
            MediaTemplateRequest request = new MediaTemplateRequest();
            request.setBucketName(cosProperties.getBucket());
            request.setTemplateId(templateId);
            Boolean result = cosClient.deleteMediaTemplate(request);
            // 删除成功后清除相关缓存
            if (result && templateName != null) {
                clearMediaTranscodeTemplateCache(templateName);
            }
            return result;
        } catch (Exception e) {
            log.error("删除媒体处理模板失败，模板ID: {}", templateId, e);
            throw new COSException("删除媒体处理模板失败");
        }
    }

    /**
     * 根据模板ID查询模板名称
     *
     * @param templateId 模板ID
     * @return 模板名称
     */
    private String getMediaTranscodeTemplateNameById(String templateId) {
        try {
            MediaTemplateRequest request = new MediaTemplateRequest();
            request.setBucketName(cosProperties.getBucket());
            request.setTag("Transcode");
            request.setTemplateId(templateId);
            MediaListTemplateResponse response = cosClient.describeMediaTemplates(request);
            if (!CollectionUtils.isEmpty(response.getTemplateList())) {
                return response.getTemplateList().getFirst().getName();
            }
        } catch (Exception e) {
            log.warn("查询模板名称失败，模板ID: {}", templateId, e);
        }
        return null;
    }

    /**
     * 清除模板相关缓存
     *
     * @param templateName 模板名称
     */
    private void clearMediaTranscodeTemplateCache(String templateName) {
        try {
            // 清除模板ID缓存
            String cacheKey = CacheConstant.REDIS_COS_MEDIA_TRANSCODE_PREFIX + templateName;
            stringRedisTemplate.delete(cacheKey);
            log.info("已清除模板缓存，模板名称: {}, 缓存键: {}", templateName, cacheKey);
        } catch (Exception e) {
            log.warn("清除模板缓存失败，模板名称: {}", templateName, e);
        }
    }

    /**
     * 创建音视频转码任务
     *
     * @param inputObject  输入文件对象键或URL
     * @param outputObject 输出文件对象键或URL
     * @param templateId   转码模板ID
     * @return 任务ID
     * @throws COSException 如果创建转码任务失败
     */
    @Override
    public String createMediaTranscodeJob(String inputObject, String outputObject, String templateId) {
        try {
            log.info("创建音视频转码任务，输入: {}, 模板ID: {}", inputObject, templateId);
            inputObject = getKeyByUrl(inputObject);
            outputObject = getKeyByUrl(outputObject);

            MediaJobsRequestV2 request = new MediaJobsRequestV2();
            request.setBucketName(cosProperties.getBucket());
            request.setTag("Transcode");
            request.getInput().setObject(inputObject);
            request.getOperation().setTemplateId(templateId);
            request.getOperation().getOutput().setBucket(cosProperties.getBucket());
            request.getOperation().getOutput().setRegion(cosProperties.getRegion());
            request.getOperation().getOutput().setObject(outputObject);

            MediaJobResponseV2 response = cosClient.createMediaJobsV2(request);
            String jobId = response.getJobsDetail().getJobId();
            log.info("创建音视频转码任务成功，任务ID: {}", jobId);
            return jobId;
        } catch (Exception e) {
            log.error("创建音视频转码任务失败，输入: {}, 模板ID: {}", inputObject, templateId, e);
            throw new COSException("创建转码任务失败");
        }
    }

    /**
     * 创建并标记媒体转码任务
     *
     * @param templateName 转码模板名称
     * @param inputUrl 输入文件URL
     * @param hash 文件hash值
     * @return 任务ID
     * @throws COSException 如果创建转码任务失败
     */
    @Override
    public String createAndMarkMediaTranscodeJob(String templateName, String inputUrl, String hash) {
        log.info("创建并标记媒体转码任务，模板名称: {}, 输入URL: {}, hash: {}", templateName, inputUrl, hash);
        String jobId;
        // 根据模板名称决定调用的方法
        if (MediaTranscodeTemplateEnum.isValidTemplateName(templateName)) {
            // 音频转码模板，使用原有的模板方式
            String templateId = getCachedMediaTranscodeTemplate(templateName);
            String outputKey = generateOutputKey(inputUrl, templateName, hash);
            jobId = createMediaTranscodeJob(inputUrl, outputKey, templateId);
        } else if (MediaTranscodeFormatEnum.isValidTemplateName(templateName)) {
            // 视频转码格式，使用新的格式方式
            jobId = createMediaTranscodeFormatJob(inputUrl, templateName, hash);
        } else {
            throw new IllegalArgumentException("不支持的转码模板名称或格式: " + templateName);
        }
        // 如果提供了hash值，标记为已转码
        if (!StringUtils.isNullOrEmpty(hash)) {
            markMediaTranscodeJob(templateName, inputUrl, hash);
        }
        return jobId;
    }

    /**
     * 生成输出文件路径
     * 如果传了hash：格式为 MediaTranscode/模板名称/hash+URL文件后缀
     * 如果没传hash：格式为 MediaTranscode/模板名称/文件名称
     */
    private String generateOutputKey(String inputUrl, String templateName, String hash) {
        if (!StringUtils.isNullOrEmpty(hash)) {
            String fileExtension = getFileExtension(inputUrl);
            return "MediaTranscode/" + templateName + "/" + hash + fileExtension;
        } else {
            // 没有hash时使用原文件名
            String inputKey = getKeyByUrl(inputUrl);
            return "MediaTranscode/" + templateName + "/" + inputKey;
        }
    }

    /**
     * 查询媒体转码任务详情
     * <p>
     * 任务状态：
     * Submitted：已提交，待执行、Running：执行中、Success：执行成功
     * Failed：执行失败、Pause：任务暂停，当暂停队列时，待执行的任务会变为暂停状态、Cancel：任务被取消执行
     *
     * @param jobId 任务ID
     * @return 任务详情对象
     * @throws COSException 如果查询任务失败
     */
    @Override
    public COSMediaTranscodeJob getMediaTranscodeJob(String jobId) {
        try {
            log.debug("查询媒体任务详情，任务ID: {}", jobId);
            MediaJobsRequest request = new MediaJobsRequest();
            request.setBucketName(cosProperties.getBucket());
            request.setJobId(jobId);
            MediaJobResponse response = cosClient.describeMediaJob(request);
            log.debug("查询媒体任务状态，任务ID: {}, 详情: {}", jobId, JsonUtil.toJsonString(response));
            return new COSMediaTranscodeJob(
                    jobId,
                    response.getJobsDetail().getState(),
                    response.getJobsDetail().getCode(),
                    response.getJobsDetail().getMessage(),
                    "Success".equals(response.getJobsDetail().getState()) ?
                            getUrlByKey(response.getJobsDetail().getOperation().getOutput().getObject()) : null
            );
        } catch (Exception e) {
            log.error("查询媒体任务失败，任务ID: {}", jobId, e);
            throw new COSException("查询媒体任务失败");
        }
    }

    /**
     * 取消音视频转码任务
     *
     * @param jobId 任务ID
     * @return 取消是否成功
     * @throws COSException 如果取消任务失败
     */
    @Override
    public Boolean cancelMediaTranscodeJob(String jobId) {
        try {
            log.info("取消媒体任务，任务ID: {}", jobId);
            MediaJobsRequest request = new MediaJobsRequest();
            request.setBucketName(cosProperties.getBucket());
            request.setJobId(jobId);
            Boolean result = cosClient.cancelMediaJob(request);
            log.info("取消媒体任务成功，任务ID: {}", jobId);
            return result;
        } catch (Exception e) {
            log.error("取消媒体任务失败，任务ID: {}", jobId, e);
            throw new COSException("取消媒体任务失败");
        }
    }

    /**
     * 标记文件已转码
     *
     * @param templateName 模板名称
     * @param url          文件URL
     * @param hash         文件hash值
     */
    @Override
    public void markMediaTranscodeJob(String templateName, String url, String hash) {
        try {
            String filterName = CacheConstant.REDIS_COS_MEDIA_TRANSCODE_BLOOM_FILTER_PREFIX + templateName;
            initMediaTranscodeBloomFilter(filterName);
            // 生成布隆过滤器元素：MediaTranscode/模板名称/hash+URL文件后缀
            String bloomElement = "MediaTranscode/" + templateName + "/" + hash + getFileExtension(url);
            // 添加到布隆过滤器
            bloomFilterUtil.addElement(filterName, bloomElement);
            log.info("文件已标记为已转码，templateName: {}，hash: {}，元素: {}", templateName, hash, bloomElement);
        } catch (Exception e) {
            log.error("标记转码状态失败，templateName: {}，hash: {}", templateName, hash, e);
        }
    }

    /**
     * 检查文件是否已转码并返回文件路径
     *
     * @param templateName 模板名称
     * @param url          文件URL
     * @param hash         文件hash值
     * @return 如果已转码返回文件路径，否则返回null
     */
    @Override
    public String checkMediaTranscodeJob(String templateName, String url, String hash) {
        try {
            String filterName = CacheConstant.REDIS_COS_MEDIA_TRANSCODE_BLOOM_FILTER_PREFIX + templateName;
            // 初始化布隆过滤器
            initMediaTranscodeBloomFilter(filterName);
            // 生成布隆过滤器元素：MediaTranscode/模板名称/hash+URL文件后缀
            String fileExtension = getFileExtension(url);
            String bloomElement = "MediaTranscode/" + templateName + "/" + hash + fileExtension;
            // 检查布隆过滤器
            if (!bloomFilterUtil.containsElement(filterName, bloomElement)) {
                log.debug("布隆过滤器中不存在该元素，文件未转码，templateName: {}, hash: {}", templateName, hash);
                return null;
            }
            // 布隆过滤器存在，再检查COS文件是否真实存在
            return doesObjectExist(bloomElement) ? getUrlByKey(bloomElement) : null;
        } catch (Exception e) {
            log.error("检查转码文件失败，hash: {}", hash, e);
            return null;
        }
    }

    /**
     * 初始化转码布隆过滤器
     *
     * @param filterName 过滤器名称
     */
    private void initMediaTranscodeBloomFilter(String filterName) {
        try {
            if (!bloomFilterUtil.isExists(filterName)) {
                // 预期插入10万个转码记录，误判率0.001
                bloomFilterUtil.initBloomFilter(filterName, 100000, 0.001);
                log.info("初始化转码布隆过滤器成功，过滤器名称: {}", filterName);
            }
        } catch (Exception e) {
            log.error("初始化转码布隆过滤器失败，过滤器名称: {}", filterName, e);
        }
    }

    /**
     * 获取文件后缀
     *
     * @param url 文件URL
     * @return 文件后缀，如果没有后缀则返回空字符串
     */
    private String getFileExtension(String url) {
        if (url != null && url.contains(".")) {
            return url.substring(url.lastIndexOf("."));
        }
        return "";
    }

    /**
     * 检查COS对象是否存在
     *
     * @param key 对象键
     * @return 是否存在
     */
    @Override
    public boolean doesObjectExist(String key) {
        return cosClient.doesObjectExist(cosProperties.getBucket(), key);
    }

    /**
     * 获取媒体信息
     *
     * @param inputObjectKey 输入文件对象键
     * @return 媒体信息
     */
    @Override
    public MediaInfoResponse getMediaInfo(String inputObjectKey) {
        MediaInfoRequest mediaInfoRequest = new MediaInfoRequest();
        mediaInfoRequest.setBucketName(cosProperties.getBucket());
        mediaInfoRequest.getInput().setObject(inputObjectKey);
        return cosClient.generateMediainfo(mediaInfoRequest);
    }

    /**
     * 创建音视频转码任务
     *
     * @param inputObjectKey 输入文件对象键或URL
     * @param format         转码格式
     * @param hash           文件hash值
     * @return 任务ID
     */
    public String createMediaTranscodeFormatJob(String inputObjectKey, String format, String hash) {
        inputObjectKey = getKeyByUrl(inputObjectKey);
        // 获取媒体信息响应对象
        MediaInfoResponse mediaInfoResponse = getMediaInfo(inputObjectKey);
        MediaInfoStream stream = mediaInfoResponse.getMediaInfo().getStream();
        MediaInfoAudio mediaInfoAudio = stream.getMediaInfoAudioList().getFirst();
        MediaInfoVideo mediaInfoVideo = stream.getMediaInfoVideoList().getFirst();

        //1.创建任务请求对象
        MediaJobsRequestV2 request = new MediaJobsRequestV2();
        //2.添加请求参数 参数详情请见api接口文档
        request.setBucketName(cosProperties.getBucket());
        request.setTag("Transcode");
        request.getInput().setObject(inputObjectKey);
        //2.1添加媒体任务操作参数
        MediaTranscodeObject transcode = request.getOperation().getTranscode();

        // 根据容器格式配置转码参数
        configureTranscode(transcode, format, mediaInfoVideo, mediaInfoAudio);
        // 根据格式生成输出文件路径
        String extension;
        if (format.startsWith("MP4_")) {
            extension = ".mp4";
        } else if (format.startsWith("MKV_")) {
            extension = ".mkv";
        } else if (format.startsWith("AVI_")) {
            extension = ".avi";
        } else if (format.startsWith("MOV_")) {
            extension = ".mov";
        } else if (format.startsWith("WEBM_")) {
            extension = ".webm";
        } else if (format.startsWith("HLS_")) {
            // HLS格式会自动拼接.m3u8后缀
            extension = "";
        } else {
            throw new IllegalArgumentException("不支持的格式: " + format);
        }
        String outputObjectKey = generateVideoOutputKey(inputObjectKey, format, hash, extension);

        request.getOperation().getOutput().setBucket(cosProperties.getBucket());
        request.getOperation().getOutput().setRegion(cosProperties.getRegion());
        request.getOperation().getOutput().setObject(outputObjectKey);
        //3.调用接口,获取任务响应对象
        log.info("inputObjectKey: {}，创建音视频转码任务请求，format: {}", inputObjectKey, format);
        MediaJobResponseV2 response = cosClient.createMediaJobsV2(request);
        String jobId = response.getJobsDetail().getJobId();
        log.info("inputObjectKey: {}，创建音视频转码任务成功，format: {}，任务ID: {}，outputObjectKey: {}", inputObjectKey, format, jobId, outputObjectKey);
        return jobId;
    }

    /**
     * 配置转码参数
     */
    private void configureTranscode(MediaTranscodeObject transcode, String format,
                                    MediaInfoVideo mediaInfoVideo, MediaInfoAudio mediaInfoAudio) {
        MediaContainerObject container = transcode.getContainer();
        //container.setClipConfig(new MediaClipConfig());

        // 根据格式前缀设置容器格式
        if (format.startsWith("MP4_")) {
            container.setFormat("mp4");
        } else if (format.startsWith("MKV_")) {
            container.setFormat("mkv");
        } else if (format.startsWith("AVI_")) {
            container.setFormat("avi");
        } else if (format.startsWith("MOV_")) {
            container.setFormat("mov");
        } else if (format.startsWith("WEBM_")) {
            container.setFormat("webm");
        } else if (format.startsWith("HLS_")) {
            container.setFormat("hls");
        }
        
        // 只有当视频信息存在时才配置视频参数
        if (mediaInfoVideo != null && !StringUtils.isNullOrEmpty(mediaInfoVideo.getCodecName())) {
            MediaTranscodeVideoObject video = transcode.getVideo();
            video.setRemove("false");

            // 根据格式设置视频编码器
            if (format.contains("H264")) {
                video.setCodec("h.264");
            } else if (format.contains("H265")) {
                video.setCodec("h.265");
            } else if (format.contains("H266")) {
                video.setCodec("h.266");
            } else if (format.contains("AV1")) {
                video.setCodec("av1");
            } else if (format.contains("VP8")) {
                video.setCodec("vp8");
            } else if (format.contains("VP9")) {
                video.setCodec("vp9");
            }
            
            // 设置视频码率为mediaInfoVideo的Bitrate的整数部分（向下取整）
            if (mediaInfoVideo.getBitrate() != null) {
                video.setBitrate(new BigDecimal(mediaInfoVideo.getBitrate()).setScale(0, RoundingMode.DOWN).toString());
            }
            // 设置分辨率
            if (format.contains("360P")) {
                video.setWidth("640");
                video.setHeight("360");
            } else if (format.contains("480P")) {
                video.setWidth("854");
                video.setHeight("480");
            } else if (format.contains("720P")) {
                video.setWidth("1280");
                video.setHeight("720");
            } else if (format.contains("1080P")) {
                video.setWidth("1920");
                video.setHeight("1080");
            } else {
                // 使用原视频的宽和高
                video.setWidth(mediaInfoVideo.getWidth());
                video.setHeight(mediaInfoVideo.getHeight());
            }
        }

        // 只有当音频信息存在时才配置音频参数
        if (mediaInfoAudio != null && !StringUtils.isNullOrEmpty(mediaInfoAudio.getCodecName())) {
            MediaAudioObject audio = transcode.getAudio();
            audio.setRemove("false");

            if (format.startsWith("WEBM_")) {
                // WebM音频配置
                if (Arrays.asList("vorbis", "opus").contains(mediaInfoAudio.getCodecName())) {
                    audio.setCodec(mediaInfoAudio.getCodecName());
                    audio.setSamplerate(mediaInfoAudio.getSampleRate());
                } else {
                    audio.setCodec("vorbis");
                }
            } else {
                // 其他格式音频配置
                if (Arrays.asList("aac", "mp3").contains(mediaInfoAudio.getCodecName())) {
                    audio.setCodec(mediaInfoAudio.getCodecName());
                    audio.setSamplerate(mediaInfoAudio.getSampleRate());
                    audio.setChannels(mediaInfoAudio.getChannel());
                } else {
                    audio.setCodec("aac");
                }
            }

            // 设置音频码率为mediaInfoAudio的Bitrate的整数部分（向下取整）
            if (mediaInfoAudio.getBitrate() != null) {
                audio.setBitrate(new BigDecimal(mediaInfoAudio.getBitrate()).setScale(0, RoundingMode.DOWN).toString());
            }
        }

        transcode.getTransConfig().setDeleteMetadata("false");
    }



    /**
     * 生成视频转码输出文件路径
     * 如果传了hash：格式为 MediaTranscode/模板名称/hash+目标文件后缀
     * 如果没传hash：格式为 MediaTranscode/模板名称/文件名称+目标文件后缀
     *
     * @param inputUrl      输入文件URL
     * @param templateName  模板名称
     * @param hash          文件hash值
     * @param fileExtension 目标文件后缀（如.mp4, .mkv等）
     * @return 输出文件路径
     */
    private String generateVideoOutputKey(String inputUrl, String templateName, String hash, String fileExtension) {
        if (!StringUtils.isNullOrEmpty(hash)) {
            return "MediaTranscode/" + templateName + "/" + hash + fileExtension;
        } else {
            String inputKey = getKeyByUrl(inputUrl);
            String fileNameWithoutExtension = inputKey.contains(".")
                    ? inputKey.substring(0, inputKey.lastIndexOf('.'))
                    : inputKey;
            return "MediaTranscode/" + templateName + "/" + fileNameWithoutExtension + fileExtension;
        }
    }

    /**
     * 创建视频转码任务
     *
     * @param url  输入文件URL
     * @param hash 文件hash值（可以为空）
     */
    @Override
    public void createVideoMediaTranscodeJob(String url, String hash) {
        log.info("创建视频转码任务 - URL: {}, hash: {}", url, hash);
        // 1. 获取媒体信息以检查文件大小
        String inputObjectKey = getKeyByUrl(url);
        MediaInfoResponse mediaInfoResponse = getMediaInfo(inputObjectKey);
        log.info("inputObjectKey: {}，获取媒体信息响应: {}", inputObjectKey, JSON.toJSONString(mediaInfoResponse));
        // 2. 检查文件大小是否超过20M
        long fileSizeBytes = Long.parseLong(mediaInfoResponse.getMediaInfo().getFormat().getSize());
        if (fileSizeBytes < CommonConstant.VIDEO_TRANSCODE_MAX_SIZE_BYTES) {
            log.warn("文件大小小于20M，跳过转码 - 文件: {}, 大小: {} bytes", inputObjectKey, fileSizeBytes);
            return;
        }
        // 3. 获取文件扩展名并创建转码任务
        String fileExtension = getFileExtension(inputObjectKey);
        List<MediaTranscodeFormatEnum> formats = getMediaTranscodeFormatEnums(fileExtension);
        log.info("inputObjectKey: {}，创建 {} 个转码任务", inputObjectKey, formats.size());
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            // 创建所有异步任务
            List<CompletableFuture<Map.Entry<String, String>>> futures = formats.stream()
                    .map(format -> CompletableFuture.supplyAsync(() -> {
                        String formatName = format.name();
                        // hash为空或转码文件不存在时才创建
                        if (StringUtils.isNullOrEmpty(hash) ||
                                StringUtils.isNullOrEmpty(checkMediaTranscodeJob(formatName, url, hash))) {
                            try {
                                String jobId = createAndMarkMediaTranscodeJob(formatName, url, hash);
                                return Map.entry(formatName, jobId);
                            } catch (Exception e) {
                                log.error("inputObjectKey: {}，创建音视频转码任务失败，format: {}", inputObjectKey, formatName, e);
                                throw e;
                            }
                        } else {
                            log.info("inputObjectKey: {}，转码文件已存在: {}", inputObjectKey, formatName);
                            return null; // 转码文件已存在，返回null
                        }
                    }, executor))
                    .toList();
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            // 按照formats顺序收集结果 (Format -> JobId)
            Map<String, String> formatJobIdMap = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (existing, replacement) -> existing,
                            LinkedHashMap::new));
            // 汇总输出所有Format和JobId
            log.info("inputObjectKey: {}, JobIdFormat: {}", inputObjectKey, JsonUtil.toJsonString(formatJobIdMap));
        } catch (Exception e) {
            log.error("创建转码任务失败", e);
            throw new COSException("创建转码任务失败", e);
        }
        // 4. 生成并上传HLS主播放列表
        List<MediaTranscodeFormatEnum> hlsFormats = formats.stream()
                .filter(f -> f.name().startsWith("HLS_"))
                .toList();
        if (!hlsFormats.isEmpty()) {
            generateHlsMasterPlaylist(hlsFormats, url, hash);
        }
    }

    /**
     * 获取视频转码格式枚举列表
     * 根据文件扩展名返回对应的转码格式列表
     *
     * @param fileExtension 文件扩展名
     * @return 转码格式枚举列表
     * @throws COSException 如果不支持的文件格式
     */
    private static List<MediaTranscodeFormatEnum> getMediaTranscodeFormatEnums(String fileExtension) {
        List<MediaTranscodeFormatEnum> mp4SpecificFormats = List.of(
                MediaTranscodeFormatEnum.MP4_H265,
                MediaTranscodeFormatEnum.MP4_H265_360P,
                MediaTranscodeFormatEnum.MP4_H265_480P,
                MediaTranscodeFormatEnum.MP4_H265_720P,
                MediaTranscodeFormatEnum.MP4_H265_1080P
        );
        List<MediaTranscodeFormatEnum> mkvSpecificFormats = List.of(
                MediaTranscodeFormatEnum.MKV_H265,
                MediaTranscodeFormatEnum.MKV_H265_360P,
                MediaTranscodeFormatEnum.MKV_H265_480P,
                MediaTranscodeFormatEnum.MKV_H265_720P,
                MediaTranscodeFormatEnum.MKV_H265_1080P
        );
        List<MediaTranscodeFormatEnum> webmSpecificFormats = List.of(
                MediaTranscodeFormatEnum.WEBM_VP8,
                MediaTranscodeFormatEnum.WEBM_VP8_360P,
                MediaTranscodeFormatEnum.WEBM_VP8_480P,
                MediaTranscodeFormatEnum.WEBM_VP8_720P,
                MediaTranscodeFormatEnum.WEBM_VP8_1080P,
                MediaTranscodeFormatEnum.WEBM_VP9,
                MediaTranscodeFormatEnum.WEBM_VP9_360P,
                MediaTranscodeFormatEnum.WEBM_VP9_480P,
                MediaTranscodeFormatEnum.WEBM_VP9_720P,
                MediaTranscodeFormatEnum.WEBM_VP9_1080P
        );
        List<MediaTranscodeFormatEnum> hlsSpecificFormats = List.of(
                MediaTranscodeFormatEnum.HLS_H264,
                MediaTranscodeFormatEnum.HLS_H264_360P,
                MediaTranscodeFormatEnum.HLS_H264_480P,
                MediaTranscodeFormatEnum.HLS_H264_720P,
                MediaTranscodeFormatEnum.HLS_H264_1080P,
                MediaTranscodeFormatEnum.HLS_H265,
                MediaTranscodeFormatEnum.HLS_H265_360P,
                MediaTranscodeFormatEnum.HLS_H265_480P,
                MediaTranscodeFormatEnum.HLS_H265_720P,
                MediaTranscodeFormatEnum.HLS_H265_1080P
        );
        List<MediaTranscodeFormatEnum> formats;
        if (".mp4".equalsIgnoreCase(fileExtension)) {
            formats = new ArrayList<>(hlsSpecificFormats);
            //formats.addAll(mp4SpecificFormats);
            //formats.addAll(webmSpecificFormats);
        } else if (".mkv".equalsIgnoreCase(fileExtension)) {
            formats = new ArrayList<>(hlsSpecificFormats);
            //formats.addAll(mkvSpecificFormats);
            //formats.addAll(webmSpecificFormats);
        } else if (".avi".equalsIgnoreCase(fileExtension)) {
            formats = new ArrayList<>(hlsSpecificFormats);
            //formats.addAll(webmSpecificFormats);
        } else {
            throw new COSException("不支持的视频文件转码格式: " + fileExtension);
        }
        return formats;
    }

    /**
     * 生成并上传HLS主播放列表
     * @param hlsFormats HLS转码格式列表
     * @param url        原始文件URL
     * @param hash       文件hash
     */
    private void generateHlsMasterPlaylist(List<MediaTranscodeFormatEnum> hlsFormats, String url, String hash) {
        // 格式配置映射：完整格式名称 -> [分辨率字符串, 带宽]
        Map<String, String[]> formatConfigs = Map.of(
                "HLS_H264", new String[]{"", "8000000"},
                "HLS_H265", new String[]{"", "5000000"},
                "HLS_H264_360P", new String[]{"640x360", "800000"},
                "HLS_H265_360P", new String[]{"640x360", "600000"},
                "HLS_H264_480P", new String[]{"854x480", "1400000"},
                "HLS_H265_480P", new String[]{"854x480", "1000000"},
                "HLS_H264_720P", new String[]{"1280x720", "2800000"},
                "HLS_H265_720P", new String[]{"1280x720", "2000000"},
                "HLS_H264_1080P", new String[]{"1920x1080", "5000000"},
                "HLS_H265_1080P", new String[]{"1920x1080", "3500000"}
        );
        // 分别为HLS_H264和HLS_H265构建主播放列表内容
        StringBuilder h264MasterPlaylist = new StringBuilder("#EXTM3U\n");
        StringBuilder h265MasterPlaylist = new StringBuilder("#EXTM3U\n");
        boolean hasH264Streams = false;
        boolean hasH265Streams = false;
        for (MediaTranscodeFormatEnum format : hlsFormats) {
            String formatName = format.name();
            String[] config = formatConfigs.get(formatName);
            // 如果找到配置，添加到播放列表
            if (config != null) {
                String outputKey = generateVideoOutputKey(url, formatName, hash, ".m3u8");
                String streamUrl = getUrlByKey(outputKey);
                String streamInfo = "#EXT-X-STREAM-INF:BANDWIDTH=%s,RESOLUTION=%s\n%s\n"
                        .formatted(config[1], config[0], streamUrl);
                // 根据格式类型分别添加到对应的播放列表
                if (formatName.startsWith("HLS_H264")) {
                    h264MasterPlaylist.append(streamInfo);
                    hasH264Streams = true;
                } else if (formatName.startsWith("HLS_H265")) {
                    h265MasterPlaylist.append(streamInfo);
                    hasH265Streams = true;
                }
            }
        }
        // 生成基础文件名
        String baseName;
        if (StringUtils.isNullOrEmpty(hash)) {
            String inputKey = getKeyByUrl(url);
            baseName = inputKey.contains(".")
                    ? inputKey.substring(0, inputKey.lastIndexOf('.'))
                    : inputKey;
        } else {
            baseName = hash;
        }
        // 上传HLS_H264主播放列表（如果有H264流）
        if (hasH264Streams) {
            String h264MasterPlaylistKey = "MediaTranscode/HLS_MASTER_H264/" + baseName + ".m3u8";
            uploadContent(h264MasterPlaylistKey, h264MasterPlaylist.toString(), false);
            log.info("HLS_H264主播放列表已创建并上传: {}", h264MasterPlaylistKey);
        }
        // 上传HLS_H265主播放列表（如果有H265流）
        if (hasH265Streams) {
            String h265MasterPlaylistKey = "MediaTranscode/HLS_MASTER_H265/" + baseName + ".m3u8";
            uploadContent(h265MasterPlaylistKey, h265MasterPlaylist.toString(), false);
            log.info("HLS_H265主播放列表已创建并上传: {}", h265MasterPlaylistKey);
        }
    }
}