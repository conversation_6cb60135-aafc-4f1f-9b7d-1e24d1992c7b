package com.unipus.digitalbook.model.enums;

import lombok.Getter;

/**
 * 转码格式枚举
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@Getter
public enum MediaTranscodeFormatEnum {

    // MP4 格式
    MP4_H264("MP4 container with H.264 video"),
    MP4_H264_360P("MP4 container with H.264 video at 360p"),
    MP4_H264_480P("MP4 container with H.264 video at 480p"),
    MP4_H264_720P("MP4 container with H.264 video at 720p"),
    MP4_H264_1080P("MP4 container with H.264 video at 1080p"),

    MP4_H265("MP4 container with H.265 video"),
    MP4_H265_360P("MP4 container with H.265 video at 360p"),
    MP4_H265_480P("MP4 container with H.265 video at 480p"),
    MP4_H265_720P("MP4 container with H.265 video at 720p"),
    MP4_H265_1080P("MP4 container with H.265 video at 1080p"),

    MP4_H266("MP4 container with H.266 video"),
    MP4_H266_360P("MP4 container with H.266 video at 360p"),
    MP4_H266_480P("MP4 container with H.266 video at 480p"),
    MP4_H266_720P("MP4 container with H.266 video at 720p"),
    MP4_H266_1080P("MP4 container with H.266 video at 1080p"),

    MP4_AV1("MP4 container with AV1 video"),
    MP4_AV1_360P("MP4 container with AV1 video at 360p"),
    MP4_AV1_480P("MP4 container with AV1 video at 480p"),
    MP4_AV1_720P("MP4 container with AV1 video at 720p"),
    MP4_AV1_1080P("MP4 container with AV1 video at 1080p"),

    // MKV 格式
    MKV_H264("MKV container with H.264 video"),
    MKV_H264_360P("MKV container with H.264 video at 360p"),
    MKV_H264_480P("MKV container with H.264 video at 480p"),
    MKV_H264_720P("MKV container with H.264 video at 720p"),
    MKV_H264_1080P("MKV container with H.264 video at 1080p"),

    MKV_H265("MKV container with H.265 video"),
    MKV_H265_360P("MKV container with H.265 video at 360p"),
    MKV_H265_480P("MKV container with H.265 video at 480p"),
    MKV_H265_720P("MKV container with H.265 video at 720p"),
    MKV_H265_1080P("MKV container with H.265 video at 1080p"),

    MKV_H266("MKV container with H.266 video"),
    MKV_H266_360P("MKV container with H.266 video at 360p"),
    MKV_H266_480P("MKV container with H.266 video at 480p"),
    MKV_H266_720P("MKV container with H.266 video at 720p"),
    MKV_H266_1080P("MKV container with H.266 video at 1080p"),

    // AVI 格式
    AVI_H264("AVI container with H.264 video"),
    AVI_H264_360P("AVI container with H.264 video at 360p"),
    AVI_H264_480P("AVI container with H.264 video at 480p"),
    AVI_H264_720P("AVI container with H.264 video at 720p"),
    AVI_H264_1080P("AVI container with H.264 video at 1080p"),

    // MOV 格式
    MOV_H264("MOV container with H.264 video"),
    MOV_H264_360P("MOV container with H.264 video at 360p"),
    MOV_H264_480P("MOV container with H.264 video at 480p"),
    MOV_H264_720P("MOV container with H.264 video at 720p"),
    MOV_H264_1080P("MOV container with H.264 video at 1080p"),

    MOV_H266("MOV container with H.266 video"),
    MOV_H266_360P("MOV container with H.266 video at 360p"),
    MOV_H266_480P("MOV container with H.266 video at 480p"),
    MOV_H266_720P("MOV container with H.266 video at 720p"),
    MOV_H266_1080P("MOV container with H.266 video at 1080p"),

    // WebM 格式
    WEBM_VP8("WebM container with VP8 video"),
    WEBM_VP8_360P("WebM container with VP8 video at 360p"),
    WEBM_VP8_480P("WebM container with VP8 video at 480p"),
    WEBM_VP8_720P("WebM container with VP8 video at 720p"),
    WEBM_VP8_1080P("WebM container with VP8 video at 1080p"),

    WEBM_VP9("WebM container with VP9 video"),
    WEBM_VP9_360P("WebM container with VP9 video at 360p"),
    WEBM_VP9_480P("WebM container with VP9 video at 480p"),
    WEBM_VP9_720P("WebM container with VP9 video at 720p"),
    WEBM_VP9_1080P("WebM container with VP9 video at 1080p"),

    WEBM_AV1("WebM container with AV1 video"),
    WEBM_AV1_360P("WebM container with AV1 video at 360p"),
    WEBM_AV1_480P("WebM container with AV1 video at 480p"),
    WEBM_AV1_720P("WebM container with AV1 video at 720p"),
    WEBM_AV1_1080P("WebM container with AV1 video at 1080p"),

    HLS_H264("HLS container with H.264 video"),
    HLS_H264_360P("HLS container with H.264 video at 360p"),
    HLS_H264_480P("HLS container with H.264 video at 480p"),
    HLS_H264_720P("HLS container with H.264 video at 720p"),
    HLS_H264_1080P("HLS container with H.264 video at 1080p"),

    HLS_H265("HLS container with H.265 video"),
    HLS_H265_360P("HLS container with H.265 video at 360p"),
    HLS_H265_480P("HLS container with H.265 video at 480p"),
    HLS_H265_720P("HLS container with H.265 video at 720p"),
    HLS_H265_1080P("HLS container with H.265 video at 1080p");

    private final String desc;

    MediaTranscodeFormatEnum(String desc) {
        this.desc = desc;
    }

    /**
     * 检查模板名称是否为有效的视频转码格式
     *
     * @param templateName 模板名称
     * @return 是否有效
     */
    public static boolean isValidTemplateName(String templateName) {
        if (templateName == null || templateName.trim().isEmpty()) {
            return false;
        }
        try {
            MediaTranscodeFormatEnum.valueOf(templateName.toUpperCase());
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
