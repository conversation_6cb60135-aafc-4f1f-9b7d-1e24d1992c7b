<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookKnowledgeResourceDetailInfoMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceDetailInfoPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="resourceId" column="resource_id" jdbcType="BIGINT"/>
        <result property="bookId" column="book_id" jdbcType="VARCHAR"/>
        <result property="chapterId" column="chapter_id" jdbcType="VARCHAR"/>
        <result property="knowledgeId" column="knowledge_id" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="location" column="location" jdbcType="BIGINT"/>
        <result property="sourceUrl" column="source_url" jdbcType="VARCHAR"/>
        <result property="sourceRemark" column="source_remark" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="BIT"/>
        <result property="enable" column="enable" jdbcType="BIT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,resource_id,book_id,
        chapter_id,knowledge_id,type,location,
        source_url,source_remark,status,enable,
        create_time,update_time,create_by,
        update_by
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from book_knowledge_resource_detail_info
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectBySelective"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceDetailInfoPO"
            resultMap="BaseResultMap">
        select
        *
        from book_knowledge_resource_detail_info
        <where>
            <if test="id != null">and id = #{id,jdbcType=BIGINT}</if>
            <if test="resourceId != null">and resource_id = #{resourceId,jdbcType=BIGINT}</if>
            <if test="bookId != null">and book_id = #{bookId,jdbcType=VARCHAR}</if>
            <if test="chapterId != null">and chapter_id = #{chapterId,jdbcType=VARCHAR}</if>
            <if test="knowledgeId != null">and knowledge_id = #{knowledgeId,jdbcType=VARCHAR}</if>
            <if test="type != null">and type = #{type,jdbcType=INTEGER}</if>
            <if test="location != null">and location = #{location,jdbcType=BIGINT}</if>
            <if test="sourceUrl != null">and source_url = #{sourceUrl,jdbcType=VARCHAR}</if>
            <if test="sourceRemark != null">and source_remark = #{sourceRemark,jdbcType=VARCHAR}</if>
            <if test="status != null">and status = #{status,jdbcType=BIT}</if>
            <if test="enable != null">and enable = #{enable,jdbcType=BIT}</if>
            <if test="createTime != null">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>
            <if test="updateTime != null">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>
            <if test="createBy != null">and create_by = #{createBy,jdbcType=BIGINT}</if>
            <if test="updateBy != null">and update_by = #{updateBy,jdbcType=BIGINT}</if>
        </where>
        order by location asc,id asc
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from book_knowledge_resource_detail_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceDetailInfoPO"
            useGeneratedKeys="true">
        insert into book_knowledge_resource_detail_info
        ( id, resource_id, book_id
        , chapter_id, knowledge_id, type, location
        , source_url, source_remark, status, enable
        , create_time, update_time, create_by
        , update_by)
        values ( #{id,jdbcType=BIGINT}, #{resourceId,jdbcType=BIGINT}, #{bookId,jdbcType=VARCHAR}
               , #{chapterId,jdbcType=VARCHAR}, #{knowledgeId,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}
               , #{location,jdbcType=BIGINT}
               , #{sourceUrl,jdbcType=VARCHAR}, #{sourceRemark,jdbcType=VARCHAR}, #{status,jdbcType=BIT}
               , #{enable,jdbcType=BIT}
               , #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}
               , #{updateBy,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceDetailInfoPO"
            useGeneratedKeys="true">
        insert into book_knowledge_resource_detail_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="resourceId != null">resource_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="knowledgeId != null">knowledge_id,</if>
            <if test="type != null">type,</if>
            <if test="location != null">location,</if>
            <if test="sourceUrl != null">source_url,</if>
            <if test="sourceRemark != null">source_remark,</if>
            <if test="status != null">status,</if>
            <if test="enable != null">enable,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="resourceId != null">#{resourceId,jdbcType=BIGINT},</if>
            <if test="bookId != null">#{bookId,jdbcType=VARCHAR},</if>
            <if test="chapterId != null">#{chapterId,jdbcType=VARCHAR},</if>
            <if test="knowledgeId != null">#{knowledgeId,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=INTEGER},</if>
            <if test="location != null">#{location,jdbcType=BIGINT},</if>
            <if test="sourceUrl != null">#{sourceUrl,jdbcType=VARCHAR},</if>
            <if test="sourceRemark != null">#{sourceRemark,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=BIT},</if>
            <if test="enable != null">#{enable,jdbcType=BIT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
        </trim>
    </insert>


    <insert id="batchInsertSelective"
            keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceDetailInfoPO"
            useGeneratedKeys="true">
        insert into book_knowledge_resource_detail_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].id != null">id,</if>
            <if test="list[0].resourceId != null">resource_id,</if>
            <if test="list[0].bookId != null">book_id,</if>
            <if test="list[0].chapterId != null">chapter_id,</if>
            <if test="list[0].knowledgeId != null">knowledge_id,</if>
            <if test="list[0].type != null">type,</if>
            <if test="list[0].location != null">location,</if>
            <if test="list[0].sourceUrl != null">source_url,</if>
            <if test="list[0].sourceRemark != null">source_remark,</if>
            <if test="list[0].status != null">status,</if>
            <if test="list[0].enable != null">enable,</if>
            <if test="list[0].createTime != null">create_time,</if>
            <if test="list[0].updateTime != null">update_time,</if>
            <if test="list[0].createBy != null">create_by,</if>
            <if test="list[0].updateBy != null">update_by,</if>
        </trim>
        values
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id,jdbcType=BIGINT},</if>
                <if test="item.resourceId != null">#{item.resourceId,jdbcType=BIGINT},</if>
                <if test="item.bookId != null">#{item.bookId,jdbcType=VARCHAR},</if>
                <if test="item.chapterId != null">#{item.chapterId,jdbcType=VARCHAR},</if>
                <if test="item.knowledgeId != null">#{item.knowledgeId,jdbcType=VARCHAR},</if>
                <if test="item.type != null">#{item.type,jdbcType=INTEGER},</if>
                <if test="item.location != null">#{item.location,jdbcType=BIGINT},</if>
                <if test="item.sourceUrl != null">#{item.sourceUrl,jdbcType=VARCHAR},</if>
                <if test="item.sourceRemark != null">#{item.sourceRemark,jdbcType=VARCHAR},</if>
                <if test="item.status != null">#{item.status,jdbcType=BIT},</if>
                <if test="item.enable != null">#{item.enable,jdbcType=BIT},</if>
                <if test="item.createTime != null">#{item.createTime,jdbcType=TIMESTAMP},</if>
                <if test="item.updateTime != null">#{item.updateTime,jdbcType=TIMESTAMP},</if>
                <if test="item.createBy != null">#{item.createBy,jdbcType=BIGINT},</if>
                <if test="item.updateBy != null">#{item.updateBy,jdbcType=BIGINT},</if>
            </trim>
        </foreach>
    </insert>


    <update id="updateByPrimaryKeySelective"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceDetailInfoPO">
        update book_knowledge_resource_detail_info
        <set>
            <if test="resourceId != null">
                resource_id = #{resourceId,jdbcType=BIGINT},
            </if>
            <if test="bookId != null">
                book_id = #{bookId,jdbcType=VARCHAR},
            </if>
            <if test="chapterId != null">
                chapter_id = #{chapterId,jdbcType=VARCHAR},
            </if>
            <if test="knowledgeId != null">
                knowledge_id = #{knowledgeId,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="location != null">
                location = #{location,jdbcType=BIGINT},
            </if>
            <if test="sourceUrl != null">
                source_url = #{sourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceRemark != null">
                source_remark = #{sourceRemark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=BIT},
            </if>
            <if test="enable != null">
                enable = #{enable,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByResourceIdSelective"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceDetailInfoPO">
        update book_knowledge_resource_detail_info
        <set>
            <if test="bookId != null">
                book_id = #{bookId,jdbcType=VARCHAR},
            </if>
            <if test="chapterId != null">
                chapter_id = #{chapterId,jdbcType=VARCHAR},
            </if>
            <if test="knowledgeId != null">
                knowledge_id = #{knowledgeId,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="location != null">
                location = #{location,jdbcType=BIGINT},
            </if>
            <if test="sourceUrl != null">
                source_url = #{sourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceRemark != null">
                source_remark = #{sourceRemark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=BIT},
            </if>
            <if test="enable != null">
                enable = #{enable,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
        </set>
        where resource_id = #{resourceId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeResourceDetailInfoPO">
        update book_knowledge_resource_detail_info
        set resource_id   = #{resourceId,jdbcType=BIGINT},
            book_id       = #{bookId,jdbcType=VARCHAR},
            chapter_id    = #{chapterId,jdbcType=VARCHAR},
            knowledge_id  = #{knowledgeId,jdbcType=VARCHAR},
            type          = #{type,jdbcType=INTEGER},
            location      = #{location,jdbcType=BIGINT},
            source_url    = #{sourceUrl,jdbcType=VARCHAR},
            source_remark = #{sourceRemark,jdbcType=VARCHAR},
            status        = #{status,jdbcType=BIT},
            enable        = #{enable,jdbcType=BIT},
            create_time   = #{createTime,jdbcType=TIMESTAMP},
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            create_by     = #{createBy,jdbcType=BIGINT},
            update_by     = #{updateBy,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectExistSourceUrls"
            parameterType="com.unipus.digitalbook.model.po.knowledge.BookKnowledgeSourceInfoExistPO"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM book_knowledge_resource_detail_info
        <where>
            AND `enable` = 1
            <if test="bookId != null">AND book_id = #{bookId}</if>
            <if test="chapterId != null">AND chapter_id = #{chapterId}</if>
            <if test="knowledgeId != null">AND knowledge_id = #{knowledgeId}</if>
            <if test="type != null">AND type = #{type}</if>
            <if test="bookKnowledgeId != null">AND resource_id = #{bookKnowledgeId}</if>
            AND source_url in
            <foreach collection="sourceUrlList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="ignoreIds != null">
                and id not in
                <foreach collection="ignoreIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
