<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.UserChapterProgressPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.action.UserChapterProgressPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="openId" column="open_id" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="chapterId" column="chapter_id" jdbcType="VARCHAR"/>
            <result property="chapterVersionId" column="chapter_version_id" jdbcType="BIGINT"/>
            <result property="envPartition" column="env_partition" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.unipus.digitalbook.model.po.action.UserChapterProgressPO">
        <result property="progressBit" column="progress_bit" jdbcType="BLOB" typeHandler="org.apache.ibatis.type.BlobTypeHandler"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,open_id,tenant_id,chapter_id,
        chapter_version_id,create_time,update_time
    </sql>
    <sql id="Blob_Column_List">
        progress_bit
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from user_chapter_progress
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="getProgressBit" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Blob_Column_List" />
        from user_chapter_progress
        where open_id = #{openId,jdbcType=VARCHAR}
        and tenant_id = #{tenantId,jdbcType=BIGINT}
        and chapter_version_id = #{chapterVersionId,jdbcType=BIGINT}
        <if test="envPartition == null">
            and  env_partition = ''
        </if>
        <if test="envPartition != null">
            and env_partition = #{envPartition, jdbcType=VARCHAR}
        </if>
        and enable = true
        LIMIT 1
    </select>
    <select id="getProgressList" resultMap="BaseResultMap">
        SELECT
            id,
            open_id,
            chapter_id,
            chapter_version_id,
            env_partition
        FROM user_chapter_progress
        WHERE tenant_id = #{tenantId}
          AND chapter_version_id = #{chapterVersionId}
          AND enable = true
          AND id > #{lastId}
        ORDER BY id ASC LIMIT 100
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from user_chapter_progress
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.action.UserChapterProgressPO" useGeneratedKeys="true">
        insert into user_chapter_progress
        ( id,open_id,tenant_id
        ,chapter_version_id,create_time,update_time
        ,progress_bit)
        values (#{id,jdbcType=BIGINT},#{openId,jdbcType=VARCHAR},#{tenantId,jdbcType=BIGINT}
        ,#{chapterVersionId,jdbcType=BIGINT},#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP}
        ,#{progressBit,jdbcType=BLOB})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.action.UserChapterProgressPO" useGeneratedKeys="true">
        insert into user_chapter_progress
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="openId != null">open_id,</if>
                <if test="tenantId != null">tenant_id,</if>
                <if test="chapterVersionId != null">chapter_version_id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="progressBit != null">progress_bit,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="openId != null">#{openId,jdbcType=VARCHAR},</if>
                <if test="tenantId != null">#{tenantId,jdbcType=BIGINT},</if>
                <if test="chapterVersionId != null">#{chapterVersionId,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="progressBit != null">#{progressBit,jdbcType=BLOB},</if>
        </trim>
    </insert>
    <insert id="saveProgressBit">
        INSERT INTO user_chapter_progress (
        open_id, tenant_id, chapter_id, chapter_version_id, env_partition, progress_bit
        )
        VALUES (
        #{openId, jdbcType=VARCHAR},
        #{tenantId, jdbcType=BIGINT},
        #{chapterId, jdbcType=VARCHAR},
        #{chapterVersionId, jdbcType=BIGINT},
        <choose>
            <when test="envPartition != null">
                #{envPartition, jdbcType=VARCHAR}
            </when>
            <otherwise>
                ''
            </otherwise>
        </choose>,
        #{progressBit, jdbcType=BLOB}
        )
        ON DUPLICATE KEY UPDATE
        progress_bit = #{progressBit, jdbcType=BLOB},
        update_time = NOW()
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.action.UserChapterProgressPO">
        update user_chapter_progress
        <set>
                <if test="openId != null">
                    open_id = #{openId,jdbcType=VARCHAR},
                </if>
                <if test="tenantId != null">
                    tenant_id = #{tenantId,jdbcType=BIGINT},
                </if>
                <if test="chapterVersionId != null">
                    chapter_version_id = #{chapterVersionId,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="progressBit != null">
                    progress_bit = #{progressBit,jdbcType=BLOB},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.action.UserChapterProgressPO">
        update user_chapter_progress
        set 
            open_id =  #{openId,jdbcType=VARCHAR},
            tenant_id =  #{tenantId,jdbcType=BIGINT},
            chapter_version_id =  #{chapterVersionId,jdbcType=BIGINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            progress_bit =  #{progressBit,jdbcType=BLOB}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="clearProgressBit">
        update user_chapter_progress
        set enable = false
        where   open_id = #{openId,jdbcType=VARCHAR}
        and tenant_id = #{tenantId,jdbcType=BIGINT}
        and chapter_version_id = #{chapterVersionId,jdbcType=BIGINT}
    </update>
</mapper>
