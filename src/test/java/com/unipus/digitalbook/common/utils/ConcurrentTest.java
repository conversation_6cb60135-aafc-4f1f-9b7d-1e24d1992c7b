package com.unipus.digitalbook.common.utils;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.time.Duration;
import java.util.concurrent.*;
import java.util.stream.IntStream;

@ExtendWith(MockitoExtension.class)
public class ConcurrentTest {

    private WebClient webClient;

    @BeforeEach
    public void setup() {
        this.webClient = createWebClient();
    }

    @Test
    public void test() throws InterruptedException {
        int requestCount = 2000;
        String baseUrl = "http://localhost:8889/api/auth/connectionTest?id=";
        String token = "Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxNTIwMTA4MjUzMiIsImp1aWQiOjEwNTMsImlzcyI6InNlbGYiLCJq" +
                "***************************************************************************************************" +
                "sImJhc2UiXSwiaWF0IjoxNzU0NjIzNTYzfQ.rXPPSGrG8m1KZb4YvMKXRGJBsKJ1Rq8tq6yIeBjX0Sg";

        CountDownLatch latch = new CountDownLatch(requestCount);

        // 使用更合适的线程池配置
        ExecutorService executor = Executors.newFixedThreadPool(
                Runtime.getRuntime().availableProcessors() * 16 // 根据CPU核心数调整
        );

        // 使用信号量控制实际并发数
        Semaphore semaphore = new Semaphore(2000); // 同时最多请求

        IntStream.range(0, requestCount).forEach(i -> {
            executor.submit(() -> {
                try {
                    semaphore.acquire();
                    parallelPost(i, baseUrl, token, latch);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    latch.countDown();
                } finally {
                    semaphore.release();
                }
            });
        });

        // 增加等待时间以适应高并发
        boolean await = latch.await(120, TimeUnit.SECONDS);
        System.out.println("Requests completed: " + await);

        // 等待所有任务完成
        executor.shutdown();
        if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
            executor.shutdownNow();
        }

        // 最后的阻塞请求
        blockPost(baseUrl, token);
        System.out.println("All requests completed.");
    }

    private void parallelPost(int i, String baseUrl, String token, CountDownLatch latch) {
        webClient.post()
                .uri(baseUrl + i)
                .header("Content-Type", "application/json")
                .header("Authorization", token)
                .retrieve()
                .bodyToMono(String.class)
                .doFinally(signalType -> latch.countDown())
                .subscribe();
    }

    private void blockPost(String baseUrl, String token) {
        webClient.post()
                .uri(baseUrl + "final")
                .header("Content-Type", "application/json")
                .header("Authorization", token)
                .retrieve()
                .bodyToMono(String.class)
                .block();
    }

    private static WebClient createWebClient() {
        // 优化的连接提供者配置，专为高并发设计
        ConnectionProvider connectionProvider = ConnectionProvider.builder("high-concurrency-provider")
                .maxConnections(2000)
                .pendingAcquireMaxCount(2000) // 增加等待队列大小
                .pendingAcquireTimeout(Duration.ofSeconds(60)) // 增加获取连接的超时时间
                .maxIdleTime(Duration.ofSeconds(60)) // 增加连接空闲时间
                .maxLifeTime(Duration.ofMinutes(10)) // 增加连接生命周期
                .evictInBackground(Duration.ofMinutes(2)) // 后台清理间隔
                .metrics(true) // 启用指标监控
                .fifo() // 使用FIFO队列
                .build();

        HttpClient httpClient = HttpClient.create(connectionProvider)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000) // 连接超时10秒
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.SO_REUSEADDR, true) // 地址重用
                .option(ChannelOption.TCP_NODELAY, true) // 禁用Nagle算法
                .option(ChannelOption.SO_RCVBUF, 65536) // 增加接收缓冲区
                .option(ChannelOption.SO_SNDBUF, 65536) // 增加发送缓冲区
                .responseTimeout(Duration.ofSeconds(30)) // 响应超时30秒
                .doOnConnected(conn -> {
                    conn.addHandlerLast(new ReadTimeoutHandler(30))
                            .addHandlerLast(new WriteTimeoutHandler(30));
                })
                .compress(true) // 启用压缩
                .wiretap(false); // 禁用调试日志以提高性能

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(16 * 1024 * 1024)) // 16MB
                .build();
    }


}
