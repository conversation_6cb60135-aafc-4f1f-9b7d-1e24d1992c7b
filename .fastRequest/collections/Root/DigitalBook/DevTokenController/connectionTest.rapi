{"activeGroup": "<PERSON><PERSON><PERSON>", "apiParamGroup": {}, "childList": [], "classDescription": "开发测试Token", "description": "", "directory": "/.fastRequest/collections/Root/DigitalBook/DevTokenController", "domain": "http://localhost:8889/api", "enableEnv": "local,base", "enableProject": "DigitalBook", "filePath": "/.fastRequest/collections/Root/DigitalBook/DevTokenController~connectionTest.rapi", "id": "api_com.unipus.digitalbook.controller.DevTokenController.connectionTest", "mark": false, "module": "DigitalBook", "name": "connectionTest", "paramGroup": {"binaryFilePath": "", "bodyKeyValueListJson": "", "bodyParamMap": {}, "classDescription": "开发测试Token", "className": "com.unipus.digitalbook.controller.DevTokenController", "headerParamsKeyValueList": [], "jsonDocument": "", "method": "connectionTest", "methodDescription": "connectionTest", "methodType": "POST", "multipartKeyValueListJson": "[]", "originUrl": "/auth/connectionTest", "pathParamsKeyValueListJson": "[{\"customFlag\":2,\"enabled\":true,\"key\":\"id\",\"type\":\"String\",\"value\":\"{{$randomInt}}\"}]", "postScript": "", "postType": "json", "preScript": "", "returnDocument": "{\"code\":\"状态码\",\"message\":\"响应消息\",\"data\":{\"java.lang.String\":\"No comment,Type =String\"},\"success\":\"是否成功\"}", "returnTypeParamMap": {"return": {"customFlag": 2, "enabled": true, "key": "return", "type": "Object", "value": {"code": {"comment": "状态码", "customFlag": 2, "enabled": true, "key": "code", "type": "Number", "value": 1}, "message": {"comment": "响应消息", "customFlag": 2, "enabled": true, "key": "message", "type": "String", "value": "message_u2r5w"}, "data": {"comment": "响应数据", "customFlag": 2, "enabled": true, "key": "data", "type": "Object", "value": {"java.lang.String": {"comment": "", "customFlag": 2, "enabled": true, "key": "java.lang.String", "type": "String", "value": "java.lang.String_997bj"}}}, "success": {"comment": "是否成功", "customFlag": 2, "enabled": true, "key": "success", "type": "Boolean", "value": true}}}}, "tempId": "", "url": "/auth/connectionTest", "urlEncodedKeyValueListJson": "[{\"comment\":\"\",\"customFlag\":2,\"enabled\":true,\"key\":\"id\",\"type\":\"String\",\"value\":\"{{$randomInt}}\"}]", "urlEncodedKeyValueListText": "id={{$randomInt}}", "urlParamsKeyValueListJson": "[]", "urlParamsKeyValueListText": ""}, "pmRequestId": "", "pmResponseId": "", "tempId": "api_com.unipus.digitalbook.controller.DevTokenController.connectionTest", "type": 2}