{"activeGroup": "<PERSON><PERSON><PERSON>", "apiParamGroup": {}, "childList": [], "classDescription": "开发测试Token", "description": "", "directory": "/.fastRequest/collections/Root/DigitalBook/DevTokenController", "domain": "http://127.0.0.1:8889/api", "enableEnv": "local", "enableProject": "DigitalBook", "filePath": "/.fastRequest/collections/Root/DigitalBook/DevTokenController~generateDevToken.rapi", "id": "api_com.unipus.digitalbook.controller.DevTokenController.generateDevToken", "mark": false, "module": "DigitalBook", "name": "获取开发测试Token", "paramGroup": {"binaryFilePath": "", "bodyKeyValueListJson": "", "bodyParamMap": {}, "classDescription": "开发测试Token", "className": "com.unipus.digitalbook.controller.DevTokenController", "headerParamsKeyValueList": [], "jsonDocument": "", "method": "generateDevToken", "methodDescription": "获取开发测试Token", "methodType": "GET", "multipartKeyValueListJson": "[]", "originUrl": "/auth/accessToken", "pathParamsKeyValueListJson": "[]", "postScript": "\nimport com.alibaba.fastjson.JSON\nJSON data = JSON.parseObject(rfr.response.body()).getJSONObject(\"data\")\nString token = data.getString(\"access_token\")\nrfr.environment.put(\"access_token\",\"Bearer ${token}\")\n", "postType": "json", "preScript": "", "returnDocument": "{\"code\":\"状态码\",\"message\":\"响应消息\",\"data\":{\"threshold\":\"No comment,Type =Number\",\"loadFactor\":\"No comment,Type =Number\"},\"success\":\"是否成功\"}", "returnTypeParamMap": {"return": {"customFlag": 2, "enabled": true, "key": "return", "type": "Object", "value": {"code": {"comment": "状态码", "customFlag": 2, "enabled": true, "key": "code", "type": "Number", "value": 1}, "message": {"comment": "响应消息", "customFlag": 2, "enabled": true, "key": "message", "type": "String", "value": "message_m194k"}, "data": {"comment": "响应数据", "customFlag": 2, "enabled": true, "key": "data", "type": "Object", "value": {"threshold": {"comment": "", "customFlag": 2, "enabled": true, "key": "threshold", "type": "Number", "value": 1}, "loadFactor": {"comment": "", "customFlag": 2, "enabled": true, "key": "loadFactor", "type": "Number", "value": 1.0}}}, "success": {"comment": "是否成功", "customFlag": 2, "enabled": true, "key": "success", "type": "Boolean", "value": true}}}}, "tempId": "", "url": "/auth/accessToken", "urlEncodedKeyValueListJson": "[]", "urlEncodedKeyValueListText": "", "urlParamsKeyValueListJson": "[{\"comment\":\"\",\"customFlag\":2,\"enabled\":true,\"key\":\"userId\",\"type\":\"Number\",\"value\":\"1053\"},{\"comment\":\"\",\"customFlag\":2,\"enabled\":true,\"key\":\"orgId\",\"type\":\"Number\",\"value\":\"3\"}]", "urlParamsKeyValueListText": "userId=1053\n&orgId=3"}, "pmRequestId": "", "pmResponseId": "", "tempId": "api_com.unipus.digitalbook.controller.DevTokenController.generateDevToken", "type": 2}