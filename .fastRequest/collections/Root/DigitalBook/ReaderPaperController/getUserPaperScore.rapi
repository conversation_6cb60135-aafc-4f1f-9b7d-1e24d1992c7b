{"activeGroup": "<PERSON><PERSON><PERSON>", "apiParamGroup": {}, "childList": [], "classDescription": "读者试卷作答相关接口", "description": "", "directory": "/.fastRequest/collections/Root/DigitalBook/ReaderPaperController", "domain": "http://localhost:8889/api", "enableEnv": "local,base", "enableProject": "DigitalBook", "filePath": "/.fastRequest/collections/Root/DigitalBook/ReaderPaperController~getUserPaperScore.rapi", "id": "api_com.unipus.digitalbook.controller.reader.ReaderPaperController.getUserPaperScore", "mark": false, "module": "DigitalBook", "name": "取得用户试卷成绩(评价)", "paramGroup": {"binaryFilePath": "", "bodyKeyValueListJson": "{\n  \"paperId\": \"m2qBYzCZ1exqjstA\",\n  \"versionNumber\": \"0\",\n  \"testMode\": 1\n}", "bodyParamMap": {"param": {"customFlag": 2, "enabled": true, "key": "param", "type": "Object", "value": {"paperId": {"comment": "试卷ID (UUID)", "customFlag": 2, "enabled": true, "key": "paperId", "type": "String", "value": "paperId_agemq"}, "versionNumber": {"comment": "试卷版本号", "customFlag": 2, "enabled": true, "key": "versionNumber", "type": "String", "value": "versionNumber_g7fen"}, "testMode": {"comment": "诊断卷测试模式, 1:诊断模式，2:推荐模式。(诊断卷以外无视)", "customFlag": 2, "enabled": true, "key": "testMode", "type": "Number", "value": 1}}}}, "classDescription": "读者试卷作答相关接口", "className": "com.unipus.digitalbook.controller.reader.ReaderPaperController", "headerParamsKeyValueList": [], "jsonDocument": "{\n  \"paperId\": \"试卷ID (UUID)\",\n  \"versionNumber\": \"试卷版本号\",\n  \"testMode\": \"诊断卷测试模式, 1:诊断模式，2:推荐模式。(诊断卷以外无视)\"\n}", "method": "getUserPaperScore", "methodDescription": "取得用户试卷成绩(评价)", "methodType": "POST", "multipartKeyValueListJson": "[]", "originUrl": "/reader/paper/answer/getUserPaperScore", "pathParamsKeyValueListJson": "[]", "postScript": "", "postType": "json", "preScript": "", "returnDocument": "{\"code\":\"状态码\",\"message\":\"响应消息\",\"data\":{\"paperId\":\"试卷ID\",\"versionNumber\":\"试卷版本\",\"paperType\":\"试卷类型\",\"scoreBatchId\":\"成绩提交批次ID\",\"instanceId\":\"试卷实例ID/轮次ID\",\"status\":\"试卷成绩提交状态\",\"userScore\":\"用户得分\",\"standardScore\":\"试卷标准分\",\"paperScore\":{}},\"success\":\"是否成功\"}", "returnTypeParamMap": {"return": {"customFlag": 2, "enabled": true, "key": "return", "type": "Object", "value": {"code": {"comment": "状态码", "customFlag": 2, "enabled": true, "key": "code", "type": "Number", "value": 1}, "message": {"comment": "响应消息", "customFlag": 2, "enabled": true, "key": "message", "type": "String", "value": "message_ucww1"}, "data": {"comment": "响应数据", "customFlag": 2, "enabled": true, "key": "data", "type": "Object", "value": {"paperId": {"comment": "试卷ID", "customFlag": 2, "enabled": true, "key": "paperId", "type": "String", "value": "paperId_obrvq"}, "versionNumber": {"comment": "试卷版本", "customFlag": 2, "enabled": true, "key": "versionNumber", "type": "String", "value": "versionNumber_p75xz"}, "paperType": {"comment": "试卷类型", "customFlag": 2, "enabled": true, "key": "paperType", "type": "Number", "value": 1}, "scoreBatchId": {"comment": "成绩提交批次ID", "customFlag": 2, "enabled": true, "key": "scoreBatchId", "type": "String", "value": "scoreBatchId_41w0d"}, "instanceId": {"comment": "试卷实例ID/轮次ID", "customFlag": 2, "enabled": true, "key": "instanceId", "type": "String", "value": "instanceId_j179c"}, "status": {"comment": "试卷成绩提交状态", "customFlag": 2, "enabled": true, "key": "status", "type": "Number", "value": 1}, "userScore": {"comment": "用户得分", "customFlag": 2, "enabled": true, "key": "userScore", "type": "Number", "value": 1}, "standardScore": {"comment": "试卷标准分", "customFlag": 2, "enabled": true, "key": "standardScore", "type": "Number", "value": 1}, "paperScore": {"comment": "试卷成绩对象", "customFlag": 2, "enabled": true, "key": "paperScore", "type": "Object", "value": {}}}}, "success": {"comment": "是否成功", "customFlag": 2, "enabled": true, "key": "success", "type": "Boolean", "value": true}}}}, "tempId": "", "url": "/reader/paper/answer/getUserPaperScore", "urlEncodedKeyValueListJson": "[]", "urlEncodedKeyValueListText": "", "urlParamsKeyValueListJson": "[]", "urlParamsKeyValueListText": ""}, "pmRequestId": "", "pmResponseId": "", "tempId": "api_com.unipus.digitalbook.controller.reader.ReaderPaperController.getUserPaperScore", "type": 2}