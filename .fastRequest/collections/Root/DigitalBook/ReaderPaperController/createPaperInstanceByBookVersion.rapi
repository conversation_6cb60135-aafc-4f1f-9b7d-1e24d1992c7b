{"activeGroup": "<PERSON><PERSON><PERSON>", "apiParamGroup": {}, "childList": [], "classDescription": "读者试卷作答相关接口", "description": "", "directory": "/.fastRequest/collections/Root/DigitalBook/ReaderPaperController", "domain": "http://localhost:8889/api", "enableEnv": "local,base", "enableProject": "DigitalBook", "filePath": "/.fastRequest/collections/Root/DigitalBook/ReaderPaperController~createPaperInstanceByBookVersion.rapi", "id": "api_com.unipus.digitalbook.controller.reader.ReaderPaperController.createPaperInstanceByBookVersion", "mark": false, "module": "DigitalBook", "name": "通过教材信息创建试卷实例(学生作答无答案返回，老师作答有答案返回)", "paramGroup": {"binaryFilePath": "", "bodyKeyValueListJson": "{\n  \"bookId\": \"vlZSroBiSOmu5XfbvNJcSQ\",\n  \"bookVersionNumber\": \"UtYzKq5\",\n  \"paperId\": \"m2qBYzCZ1exqjstA\",\n  \"testMode\": 1\n}", "bodyParamMap": {"param": {"customFlag": 2, "enabled": true, "key": "param", "type": "Object", "value": {"bookId": {"comment": "教材ID", "customFlag": 2, "enabled": true, "key": "bookId", "type": "String", "value": "bookId_o86lh"}, "bookVersionNumber": {"comment": "教材版本(已发布的教材版本号)", "customFlag": 2, "enabled": true, "key": "bookVersionNumber", "type": "String", "value": "bookVersionNumber_bu53i"}, "paperId": {"comment": "试卷ID (UUID)", "customFlag": 2, "enabled": true, "key": "paperId", "type": "String", "value": "paperId_kz2kl"}, "testMode": {"comment": "诊断卷的测试模式 1:诊断模式/2:推荐模式。(非诊断卷可为空值)", "customFlag": 2, "enabled": true, "key": "testMode", "type": "Number", "value": 1}, "clearPreviewHistory": {"comment": "预览初始化模式: 清空历史作答数据:true/不清空历史作答数据:false", "customFlag": 2, "enabled": true, "key": "clearPreviewHistory", "type": "Boolean", "value": true}}}}, "classDescription": "读者试卷作答相关接口", "className": "com.unipus.digitalbook.controller.reader.ReaderPaperController", "headerParamsKeyValueList": [], "jsonDocument": "{\n  \"bookId\": \"教材ID\",\n  \"bookVersionNumber\": \"教材版本(已发布的教材版本号)\",\n  \"paperId\": \"试卷ID (UUID)\",\n  \"testMode\": \"诊断卷的测试模式 1:诊断模式/2:推荐模式。(非诊断卷可为空值)\",\n  \"clearPreviewHistory\": \"预览初始化模式: 清空历史作答数据:true/不清空历史作答数据:false\"\n}", "method": "createPaperInstanceByBookVersion", "methodDescription": "通过教材信息创建试卷实例(学生作答无答案返回，老师作答有答案返回)", "methodType": "POST", "multipartKeyValueListJson": "[]", "originUrl": "/reader/paper/instance/createPaperInstanceByPublishedBook", "pathParamsKeyValueListJson": "[]", "postScript": "", "postType": "json", "preScript": "", "returnDocument": "{\"code\":\"状态码\",\"message\":\"响应消息\",\"data\":{\"instanceId\":\"试卷实例ID（轮次ID）\",\"paperId\":\"试卷ID\",\"paperType\":\"试卷类型\",\"paperName\":\"试卷名称\",\"versionNumber\":\"试卷版本号\",\"description\":\"试卷说明\",\"questionCount\":\"题目数量\",\"totalScore\":\"试卷总分\",\"content\":\"试卷内容\",\"bigQuestionGroupDTOs\":[{\"groupId\":\"题组ID\",\"versionNumber\":\"题组版本\",\"groupType\":\"题组类型\",\"direction\":\"题组说明信息\",\"material\":\"题组内容列表\",\"groupDifficulty\":\"难度星级 1-5\",\"list\":[{\"id\":\"题目ID\",\"childId\":\"子题目ID\",\"questionType\":\"题型\",\"analysis\":\"小题解析\",\"quesText\":\"题干\",\"quesTextString\":\"文本题干\",\"isScoring\":\"是否计分\",\"isJudgment\":\"是否自动判题\",\"score\":\"题目\",\"difficulty\":\"难度\",\"role\":\"角色\",\"media\":\"媒体\",\"phoneticSymbol\":\"音标\",\"answerTime\":\"答题间隔时间\",\"prepareTime\":\"语音准备时间\",\"answerWordLimit\":\"答题字数限制\",\"evaluationText\":\"评测文本\",\"keywords\":[{\"keywordId\":\"关键字id\",\"keywordValue\":\"关键字值\"}],\"relevancyList\":[{\"id\":\"关联id\",\"type\":\"关联类型\"}],\"options\":[{\"label\":\"选项名称，如 A、B、C、D\",\"optionId\":\"选项的Id\",\"optionValue\":\"选项的文本内容\"}],\"answers\":[{\"answerValue\":\"答案的文本内容\",\"answerId\":\"答案的id\"}],\"children\":[{\"id\":\"题目ID\",\"childId\":\"子题目ID\",\"questionType\":\"题型\",\"analysis\":\"小题解析\",\"quesText\":\"题干\",\"quesTextString\":\"文本题干\",\"isScoring\":\"是否计分\",\"isJudgment\":\"是否自动判题\",\"score\":\"题目\",\"difficulty\":\"难度\",\"role\":\"角色\",\"media\":\"媒体\",\"phoneticSymbol\":\"音标\",\"answerTime\":\"答题间隔时间\",\"prepareTime\":\"语音准备时间\",\"answerWordLimit\":\"答题字数限制\",\"evaluationText\":\"评测文本\",\"keywords\":[{\"keywordId\":\"关键字id\",\"keywordValue\":\"关键字值\"}],\"relevancyList\":[{\"id\":\"关联id\",\"type\":\"关联类型\"}],\"options\":[{\"label\":\"选项名称，如 A、B、C、D\",\"optionId\":\"选项的Id\",\"optionValue\":\"选项的文本内容\"}],\"answers\":[{\"answerValue\":\"答案的文本内容\",\"answerId\":\"答案的id\"}],\"children\":[{\"id\":\"题目ID\",\"childId\":\"子题目ID\",\"questionType\":\"题型\",\"analysis\":\"小题解析\",\"quesText\":\"题干\",\"quesTextString\":\"文本题干\",\"isScoring\":\"是否计分\",\"isJudgment\":\"是否自动判题\",\"score\":\"题目\",\"difficulty\":\"难度\",\"role\":\"角色\",\"media\":\"媒体\",\"phoneticSymbol\":\"音标\",\"answerTime\":\"答题间隔时间\",\"prepareTime\":\"语音准备时间\",\"answerWordLimit\":\"答题字数限制\",\"evaluationText\":\"评测文本\",\"tagList\":[{}]}]}],\"tagList\":[{}]}],\"analysis\":\"题组答案解析\",\"groupSetting\":{\"answerType\":\"答题方式，如拖拽、点选\",\"pcLayoutType\":\"pc端布局类型 瀑布|分页|左右\",\"appLayoutType\":\"app端布局类型 瀑布|分页\",\"answerRole\":\"答题角色，如学生、老师\",\"answerTiming\":\"听原音 可选 'before' | 'after'| null\",\"answerLevel\":\"作答量级\",\"audioSetting\":{\"playType\":\"播放方式 点击播放 自动播放\",\"subtitle\":\"作答前显示，作答后显示\",\"setPlayNum\":\"是否设置播放次数\",\"playNum\":\"设置播放次数是true的时候，填写播放多少次\"},\"videoSetting\":{\"playType\":\"播放方式 点击播放 自动播放\",\"subtitle\":\"作答前显示，作答后显示\",\"setPlayNum\":\"是否设置视频播放次数\",\"playNum\":\"视频设置播放次数的值\"}},\"score\":\"题组总分\"}]},\"success\":\"是否成功\"}", "returnTypeParamMap": {"return": {"customFlag": 2, "enabled": true, "key": "return", "type": "Object", "value": {"code": {"comment": "状态码", "customFlag": 2, "enabled": true, "key": "code", "type": "Number", "value": 1}, "message": {"comment": "响应消息", "customFlag": 2, "enabled": true, "key": "message", "type": "String", "value": "message_338c6"}, "data": {"comment": "响应数据", "customFlag": 2, "enabled": true, "key": "data", "type": "Object", "value": {"instanceId": {"comment": "试卷实例ID（轮次ID）", "customFlag": 2, "enabled": true, "key": "instanceId", "type": "String", "value": "instanceId_z6o3a"}, "paperId": {"comment": "试卷ID", "customFlag": 2, "enabled": true, "key": "paperId", "type": "String", "value": "paperId_0ku4u"}, "paperType": {"comment": "试卷类型", "customFlag": 2, "enabled": true, "key": "paperType", "type": "Number", "value": 1}, "paperName": {"comment": "试卷名称", "customFlag": 2, "enabled": true, "key": "paperName", "type": "String", "value": "paperName_pbj2u"}, "versionNumber": {"comment": "试卷版本号", "customFlag": 2, "enabled": true, "key": "versionNumber", "type": "String", "value": "versionNumber_thglb"}, "description": {"comment": "试卷说明", "customFlag": 2, "enabled": true, "key": "description", "type": "String", "value": "description_c2xuc"}, "questionCount": {"comment": "题目数量", "customFlag": 2, "enabled": true, "key": "questionCount", "type": "Number", "value": 1}, "totalScore": {"comment": "试卷总分", "customFlag": 2, "enabled": true, "key": "totalScore", "type": "Number", "value": 1}, "content": {"comment": "试卷内容", "customFlag": 2, "enabled": true, "key": "content", "type": "String", "value": "content_9e8op"}, "bigQuestionGroupDTOs": {"comment": "题目列表", "customFlag": 2, "enabled": true, "key": "bigQuestionGroupDTOs", "type": "Array", "value": [{"groupId": {"comment": "题组ID", "customFlag": 2, "enabled": true, "key": "groupId", "type": "String", "value": "groupId_m4l0i"}, "versionNumber": {"comment": "题组版本", "customFlag": 2, "enabled": true, "key": "versionNumber", "type": "String", "value": "versionNumber_zes1h"}, "groupType": {"comment": "题组类型", "customFlag": 2, "enabled": true, "key": "groupType", "type": "String", "value": "single_choice_group"}, "direction": {"comment": "题组说明信息", "customFlag": 2, "enabled": true, "key": "direction", "type": "String", "value": "direction_vkl3m"}, "material": {"comment": "题组内容列表", "customFlag": 2, "enabled": true, "key": "material", "type": "String", "value": "material_lvkzu"}, "groupDifficulty": {"comment": "难度星级 1-5", "customFlag": 2, "enabled": true, "key": "groupDifficulty", "type": "Number", "value": 1}, "list": {"comment": "题目列表", "customFlag": 2, "enabled": true, "key": "list", "type": "Array", "value": [{"id": {"comment": "题目ID", "customFlag": 2, "enabled": true, "key": "id", "type": "String", "value": "id_9c6tr"}, "childId": {"comment": "子题目ID", "customFlag": 2, "enabled": true, "key": "childId", "type": "String", "value": "childId_6kwk3"}, "questionType": {"comment": "题型", "customFlag": 2, "enabled": true, "key": "questionType", "type": "String", "value": "questionType_24d1w"}, "analysis": {"comment": "小题解析", "customFlag": 2, "enabled": true, "key": "analysis", "type": "String", "value": "analysis_edz1l"}, "quesText": {"comment": "题干", "customFlag": 2, "enabled": true, "key": "quesText", "type": "String", "value": "quesText_gsh8i"}, "quesTextString": {"comment": "文本题干", "customFlag": 2, "enabled": true, "key": "quesTextString", "type": "String", "value": "quesTextString_1ar2h"}, "isScoring": {"comment": "是否计分", "customFlag": 2, "enabled": true, "key": "isScoring", "type": "Boolean", "value": true}, "isJudgment": {"comment": "是否自动判题", "customFlag": 2, "enabled": true, "key": "isJudgment", "type": "Boolean", "value": true}, "score": {"comment": "题目", "customFlag": 2, "enabled": true, "key": "score", "type": "Number", "value": 1}, "difficulty": {"comment": "难度", "customFlag": 2, "enabled": true, "key": "difficulty", "type": "Number", "value": 1}, "role": {"comment": "角色", "customFlag": 2, "enabled": true, "key": "role", "type": "String", "value": "role_iqox8"}, "media": {"comment": "媒体", "customFlag": 2, "enabled": true, "key": "media", "type": "String", "value": "media_j8ch8"}, "phoneticSymbol": {"comment": "音标", "customFlag": 2, "enabled": true, "key": "phoneticSymbol", "type": "String", "value": "phoneticSymbol_042lq"}, "answerTime": {"comment": "答题间隔时间", "customFlag": 2, "enabled": true, "key": "answerTime", "type": "Number", "value": 1}, "prepareTime": {"comment": "语音准备时间", "customFlag": 2, "enabled": true, "key": "prepareTime", "type": "Number", "value": 1}, "answerWordLimit": {"comment": "答题字数限制", "customFlag": 2, "enabled": true, "key": "answerWordLimit", "type": "Number", "value": 1}, "evaluationText": {"comment": "评测文本", "customFlag": 2, "enabled": true, "key": "evaluationText", "type": "String", "value": "evaluationText_6nl1e"}, "keywords": {"comment": "关键字", "customFlag": 2, "enabled": true, "key": "keywords", "type": "Array", "value": [{"keywordId": {"comment": "关键字id", "customFlag": 2, "enabled": true, "key": "keywordId", "type": "String", "value": "keywordId_e1h7j"}, "keywordValue": {"comment": "关键字值", "customFlag": 2, "enabled": true, "key": "keywordValue", "type": "String", "value": "keywordValue_w1o3d"}}]}, "relevancyList": {"comment": "关联参数", "customFlag": 2, "enabled": true, "key": "relevancyList", "type": "Array", "value": [{"id": {"comment": "关联id", "customFlag": 2, "enabled": true, "key": "id", "type": "String", "value": "id_w48vn"}, "type": {"comment": "关联类型", "customFlag": 2, "enabled": true, "key": "type", "type": "String", "value": "type_eiwue"}}]}, "options": {"comment": "选项", "customFlag": 2, "enabled": true, "key": "options", "type": "Array", "value": [{"label": {"comment": "选项名称，如 A、B、C、D", "customFlag": 2, "enabled": true, "key": "label", "type": "String", "value": "label_rc1ko"}, "optionId": {"comment": "选项的Id", "customFlag": 2, "enabled": true, "key": "optionId", "type": "String", "value": "optionId_vv6xp"}, "optionValue": {"comment": "选项的文本内容", "customFlag": 2, "enabled": true, "key": "optionValue", "type": "String", "value": "optionValue_lxrv0"}}]}, "answers": {"comment": "答案", "customFlag": 2, "enabled": true, "key": "answers", "type": "Array", "value": [{"answerValue": {"comment": "答案的文本内容", "customFlag": 2, "enabled": true, "key": "answerValue", "type": "String", "value": "answerValue_09yec"}, "answerId": {"comment": "答案的id", "customFlag": 2, "enabled": true, "key": "answerId", "type": "String", "value": "answerId_xemzz"}}]}, "children": {"comment": "子题", "customFlag": 2, "enabled": true, "key": "children", "type": "Array", "value": [{"id": {"comment": "题目ID", "customFlag": 2, "enabled": true, "key": "id", "type": "String", "value": "id_ab3pu"}, "childId": {"comment": "子题目ID", "customFlag": 2, "enabled": true, "key": "childId", "type": "String", "value": "childId_qjckb"}, "questionType": {"comment": "题型", "customFlag": 2, "enabled": true, "key": "questionType", "type": "String", "value": "questionType_tf4dm"}, "analysis": {"comment": "小题解析", "customFlag": 2, "enabled": true, "key": "analysis", "type": "String", "value": "analysis_1xgq1"}, "quesText": {"comment": "题干", "customFlag": 2, "enabled": true, "key": "quesText", "type": "String", "value": "quesText_3y0co"}, "quesTextString": {"comment": "文本题干", "customFlag": 2, "enabled": true, "key": "quesTextString", "type": "String", "value": "quesTextString_pgf29"}, "isScoring": {"comment": "是否计分", "customFlag": 2, "enabled": true, "key": "isScoring", "type": "Boolean", "value": true}, "isJudgment": {"comment": "是否自动判题", "customFlag": 2, "enabled": true, "key": "isJudgment", "type": "Boolean", "value": true}, "score": {"comment": "题目", "customFlag": 2, "enabled": true, "key": "score", "type": "Number", "value": 1}, "difficulty": {"comment": "难度", "customFlag": 2, "enabled": true, "key": "difficulty", "type": "Number", "value": 1}, "role": {"comment": "角色", "customFlag": 2, "enabled": true, "key": "role", "type": "String", "value": "role_canbv"}, "media": {"comment": "媒体", "customFlag": 2, "enabled": true, "key": "media", "type": "String", "value": "media_2iule"}, "phoneticSymbol": {"comment": "音标", "customFlag": 2, "enabled": true, "key": "phoneticSymbol", "type": "String", "value": "phoneticSymbol_uey26"}, "answerTime": {"comment": "答题间隔时间", "customFlag": 2, "enabled": true, "key": "answerTime", "type": "Number", "value": 1}, "prepareTime": {"comment": "语音准备时间", "customFlag": 2, "enabled": true, "key": "prepareTime", "type": "Number", "value": 1}, "answerWordLimit": {"comment": "答题字数限制", "customFlag": 2, "enabled": true, "key": "answerWordLimit", "type": "Number", "value": 1}, "evaluationText": {"comment": "评测文本", "customFlag": 2, "enabled": true, "key": "evaluationText", "type": "String", "value": "evaluationText_un60j"}, "keywords": {"comment": "关键字", "customFlag": 2, "enabled": true, "key": "keywords", "type": "Array", "value": [{"keywordId": {"comment": "关键字id", "customFlag": 2, "enabled": true, "key": "keywordId", "type": "String", "value": "keywordId_6yqwu"}, "keywordValue": {"comment": "关键字值", "customFlag": 2, "enabled": true, "key": "keywordValue", "type": "String", "value": "keywordValue_y69b7"}}]}, "relevancyList": {"comment": "关联参数", "customFlag": 2, "enabled": true, "key": "relevancyList", "type": "Array", "value": [{"id": {"comment": "关联id", "customFlag": 2, "enabled": true, "key": "id", "type": "String", "value": "id_ezxsu"}, "type": {"comment": "关联类型", "customFlag": 2, "enabled": true, "key": "type", "type": "String", "value": "type_n11gf"}}]}, "options": {"comment": "选项", "customFlag": 2, "enabled": true, "key": "options", "type": "Array", "value": [{"label": {"comment": "选项名称，如 A、B、C、D", "customFlag": 2, "enabled": true, "key": "label", "type": "String", "value": "label_fogpv"}, "optionId": {"comment": "选项的Id", "customFlag": 2, "enabled": true, "key": "optionId", "type": "String", "value": "optionId_1yc9y"}, "optionValue": {"comment": "选项的文本内容", "customFlag": 2, "enabled": true, "key": "optionValue", "type": "String", "value": "optionValue_r3oal"}}]}, "answers": {"comment": "答案", "customFlag": 2, "enabled": true, "key": "answers", "type": "Array", "value": [{"answerValue": {"comment": "答案的文本内容", "customFlag": 2, "enabled": true, "key": "answerValue", "type": "String", "value": "answerValue_2xxux"}, "answerId": {"comment": "答案的id", "customFlag": 2, "enabled": true, "key": "answerId", "type": "String", "value": "answerId_3xf1o"}}]}, "children": {"comment": "子题", "customFlag": 2, "enabled": true, "key": "children", "type": "Array", "value": [{"id": {"comment": "题目ID", "customFlag": 2, "enabled": true, "key": "id", "type": "String", "value": "id_i313p"}, "childId": {"comment": "子题目ID", "customFlag": 2, "enabled": true, "key": "childId", "type": "String", "value": "childId_yfypz"}, "questionType": {"comment": "题型", "customFlag": 2, "enabled": true, "key": "questionType", "type": "String", "value": "questionType_bm6si"}, "analysis": {"comment": "小题解析", "customFlag": 2, "enabled": true, "key": "analysis", "type": "String", "value": "analysis_um353"}, "quesText": {"comment": "题干", "customFlag": 2, "enabled": true, "key": "quesText", "type": "String", "value": "quesText_7q78b"}, "quesTextString": {"comment": "文本题干", "customFlag": 2, "enabled": true, "key": "quesTextString", "type": "String", "value": "quesTextString_w8667"}, "isScoring": {"comment": "是否计分", "customFlag": 2, "enabled": true, "key": "isScoring", "type": "Boolean", "value": true}, "isJudgment": {"comment": "是否自动判题", "customFlag": 2, "enabled": true, "key": "isJudgment", "type": "Boolean", "value": true}, "score": {"comment": "题目", "customFlag": 2, "enabled": true, "key": "score", "type": "Number", "value": 1}, "difficulty": {"comment": "难度", "customFlag": 2, "enabled": true, "key": "difficulty", "type": "Number", "value": 1}, "role": {"comment": "角色", "customFlag": 2, "enabled": true, "key": "role", "type": "String", "value": "role_hnky7"}, "media": {"comment": "媒体", "customFlag": 2, "enabled": true, "key": "media", "type": "String", "value": "media_6d8mo"}, "phoneticSymbol": {"comment": "音标", "customFlag": 2, "enabled": true, "key": "phoneticSymbol", "type": "String", "value": "phoneticSymbol_71o9i"}, "answerTime": {"comment": "答题间隔时间", "customFlag": 2, "enabled": true, "key": "answerTime", "type": "Number", "value": 1}, "prepareTime": {"comment": "语音准备时间", "customFlag": 2, "enabled": true, "key": "prepareTime", "type": "Number", "value": 1}, "answerWordLimit": {"comment": "答题字数限制", "customFlag": 2, "enabled": true, "key": "answerWordLimit", "type": "Number", "value": 1}, "evaluationText": {"comment": "评测文本", "customFlag": 2, "enabled": true, "key": "evaluationText", "type": "String", "value": "evaluationText_6dx9h"}, "tagList": {"comment": "标签", "customFlag": 2, "enabled": true, "key": "tagList", "type": "Array", "value": [{}]}}]}}]}, "tagList": {"comment": "标签", "customFlag": 2, "enabled": true, "key": "tagList", "type": "Array", "value": [{}]}}]}, "analysis": {"comment": "题组答案解析", "customFlag": 2, "enabled": true, "key": "analysis", "type": "String", "value": "analysis_lm00r"}, "groupSetting": {"comment": "题组设置信息，如答题方式、计分等", "customFlag": 2, "enabled": true, "key": "groupSetting", "type": "Object", "value": {"answerType": {"comment": "答题方式，如拖拽、点选", "customFlag": 2, "enabled": true, "key": "answerType", "type": "String", "value": "answerType_obbnb"}, "pcLayoutType": {"comment": "pc端布局类型 瀑布|分页|左右", "customFlag": 2, "enabled": true, "key": "pcLayoutType", "type": "String", "value": "pcLayoutType_618so"}, "appLayoutType": {"comment": "app端布局类型 瀑布|分页", "customFlag": 2, "enabled": true, "key": "appLayoutType", "type": "String", "value": "appLayoutType_b382j"}, "answerRole": {"comment": "答题角色，如学生、老师", "customFlag": 2, "enabled": true, "key": "answerRole", "type": "String", "value": "answerRole_ccfqt"}, "answerTiming": {"comment": "听原音 可选 'before' | 'after'| null", "customFlag": 2, "enabled": true, "key": "answerTiming", "type": "String", "value": "answerTiming_fmyej"}, "answerLevel": {"comment": "作答量级", "customFlag": 2, "enabled": true, "key": "answerLevel", "type": "String", "value": "answerLevel_78fe0"}, "audioSetting": {"comment": "音频设置信息", "customFlag": 2, "enabled": true, "key": "audioSetting", "type": "Object", "value": {"playType": {"comment": "播放方式 点击播放 自动播放", "customFlag": 2, "enabled": true, "key": "playType", "type": "String", "value": "playType_t3cn3"}, "subtitle": {"comment": "作答前显示，作答后显示", "customFlag": 2, "enabled": true, "key": "subtitle", "type": "String", "value": "subtitle_4l4kl"}, "setPlayNum": {"comment": "是否设置播放次数", "customFlag": 2, "enabled": true, "key": "setPlayNum", "type": "Boolean", "value": true}, "playNum": {"comment": "设置播放次数是true的时候，填写播放多少次", "customFlag": 2, "enabled": true, "key": "playNum", "type": "Number", "value": 1}}}, "videoSetting": {"comment": "视频设置信息", "customFlag": 2, "enabled": true, "key": "videoSetting", "type": "Object", "value": {"playType": {"comment": "播放方式 点击播放 自动播放", "customFlag": 2, "enabled": true, "key": "playType", "type": "String", "value": "playType_8eicg"}, "subtitle": {"comment": "作答前显示，作答后显示", "customFlag": 2, "enabled": true, "key": "subtitle", "type": "String", "value": "subtitle_wbu2r"}, "setPlayNum": {"comment": "是否设置视频播放次数", "customFlag": 2, "enabled": true, "key": "setPlayNum", "type": "Boolean", "value": true}, "playNum": {"comment": "视频设置播放次数的值", "customFlag": 2, "enabled": true, "key": "playNum", "type": "Number", "value": 1}}}}}, "score": {"comment": "题组总分", "customFlag": 2, "enabled": true, "key": "score", "type": "Number", "value": 1}}]}}}, "success": {"comment": "是否成功", "customFlag": 2, "enabled": true, "key": "success", "type": "Boolean", "value": true}}}}, "tempId": "", "url": "/reader/paper/instance/createPaperInstanceByPublishedBook", "urlEncodedKeyValueListJson": "[]", "urlEncodedKeyValueListText": "", "urlParamsKeyValueListJson": "[]", "urlParamsKeyValueListText": ""}, "pmRequestId": "", "pmResponseId": "", "tempId": "api_com.unipus.digitalbook.controller.reader.ReaderPaperController.createPaperInstanceByBookVersion", "type": 2}