{"activeGroup": "<PERSON><PERSON><PERSON>", "apiParamGroup": {}, "childList": [], "classDescription": "读者试卷作答相关接口", "description": "", "directory": "/.fastRequest/collections/Root/DigitalBook/ReaderPaperController", "domain": "http://localhost:8889/api", "enableEnv": "local,base", "enableProject": "DigitalBook", "filePath": "/.fastRequest/collections/Root/DigitalBook/ReaderPaperController~submitUserPaperAnswer.rapi", "id": "api_com.unipus.digitalbook.controller.reader.ReaderPaperController.submitUserPaperAnswer", "mark": false, "module": "DigitalBook", "name": "提交试卷作答记录", "paramGroup": {"binaryFilePath": "", "bodyKeyValueListJson": "{\n  \"async\": false,\n  \"instanceId\": \"_dsX0NXoQ3SkgxjINztR4Q\",\n  \"paperType\": 3,\n  \"userAnswerParamsMap\": {\n    \"gnKNj2aBy8aFou0X==FfEoBY2eCGbSzXcQ\": [\n      {\n        \"childId\": \"o45obbqhVrR8ojpz\",\n        \"questionType\": \"single_choice\",\n        \"userAnswerValue\": \"[\\\"rZazV8EEHUw1eGQD\\\"]\"\n      }\n    ],\n    \"Getiz6IuD3HJMlB7==RnT74jmLwEti1Deb\": [\n      {\n        \"questionId\": \"nZL7gstSC4lkwAp0\",\n        \"childId\": \"gJ7McZs4TIlSJuoe\",\n        \"questionType\": \"translation\",\n        \"userAnswerValue\": \"这是一只飞过小河的小鸟\"\n      }\n    ]\n  },\n  \"testMode\": 1,\n  \"baseInstanceId\": \"UxdJ9XwkRhWiIG-AsjFmRw\"\n}", "bodyParamMap": {"param": {"customFlag": 2, "enabled": true, "key": "param", "type": "Object", "value": {"instanceId": {"comment": "试卷实例ID/轮次ID", "customFlag": 2, "enabled": true, "key": "instanceId", "type": "String", "value": "instanceId_03hlx"}, "paperType": {"comment": "试卷类型：1:常规卷/2:挑战卷/3:诊断卷", "customFlag": 2, "enabled": true, "key": "paperType", "type": "Number", "value": 1}, "testMode": {"comment": "诊断卷测试模式, 1:诊断模式，2:推荐模式。(诊断卷以外无视)", "customFlag": 2, "enabled": true, "key": "testMode", "type": "Number", "value": 1}, "baseInstanceId": {"comment": "推荐卷诊断模式时，原推荐卷实例ID(仅诊断卷，推荐模式时赋值)", "customFlag": 2, "enabled": true, "key": "baseInstanceId", "type": "String", "value": "baseInstanceId_el4ec"}, "userAnswerParamsMap": {"comment": "用户作答记录映射(大题业务ID作为Key, 作答记录列表作为Value)", "customFlag": 2, "enabled": true, "key": "userAnswerParamsMap", "type": "Object", "value": {}}, "async": {"comment": "是否异步处理: false:同步/true:异步", "customFlag": 2, "enabled": true, "key": "async", "type": "Boolean", "value": true}}}}, "classDescription": "读者试卷作答相关接口", "className": "com.unipus.digitalbook.controller.reader.ReaderPaperController", "headerParamsKeyValueList": [], "jsonDocument": "{\n  \"instanceId\": \"试卷实例ID/轮次ID\",\n  \"paperType\": \"试卷类型：1:常规卷/2:挑战卷/3:诊断卷\",\n  \"testMode\": \"诊断卷测试模式, 1:诊断模式，2:推荐模式。(诊断卷以外无视)\",\n  \"baseInstanceId\": \"推荐卷诊断模式时，原推荐卷实例ID(仅诊断卷，推荐模式时赋值)\",\n  \"userAnswerParamsMap\": {},\n  \"async\": \"是否异步处理: false:同步/true:异步\"\n}", "method": "submitUserPaperAnswer", "methodDescription": "提交试卷作答记录", "methodType": "POST", "multipartKeyValueListJson": "[]", "originUrl": "/reader/paper/answer/submitUserPaperAnswer", "pathParamsKeyValueListJson": "[]", "postScript": "", "postType": "json", "preScript": "", "returnDocument": "{\"code\":\"状态码\",\"message\":\"响应消息\",\"data\":{\"paperId\":\"试卷ID\",\"versionNumber\":\"试卷版本\",\"paperType\":\"试卷类型\",\"scoreBatchId\":\"成绩提交批次ID\",\"instanceId\":\"试卷实例ID/轮次ID\",\"status\":\"试卷成绩提交状态\",\"userScore\":\"用户得分\",\"standardScore\":\"试卷标准分\",\"paperScore\":{}},\"success\":\"是否成功\"}", "returnTypeParamMap": {"return": {"customFlag": 2, "enabled": true, "key": "return", "type": "Object", "value": {"code": {"comment": "状态码", "customFlag": 2, "enabled": true, "key": "code", "type": "Number", "value": 1}, "message": {"comment": "响应消息", "customFlag": 2, "enabled": true, "key": "message", "type": "String", "value": "message_d9mdc"}, "data": {"comment": "响应数据", "customFlag": 2, "enabled": true, "key": "data", "type": "Object", "value": {"paperId": {"comment": "试卷ID", "customFlag": 2, "enabled": true, "key": "paperId", "type": "String", "value": "paperId_2rama"}, "versionNumber": {"comment": "试卷版本", "customFlag": 2, "enabled": true, "key": "versionNumber", "type": "String", "value": "versionNumber_llmiw"}, "paperType": {"comment": "试卷类型", "customFlag": 2, "enabled": true, "key": "paperType", "type": "Number", "value": 1}, "scoreBatchId": {"comment": "成绩提交批次ID", "customFlag": 2, "enabled": true, "key": "scoreBatchId", "type": "String", "value": "scoreBatchId_n0ck7"}, "instanceId": {"comment": "试卷实例ID/轮次ID", "customFlag": 2, "enabled": true, "key": "instanceId", "type": "String", "value": "instanceId_thczl"}, "status": {"comment": "试卷成绩提交状态", "customFlag": 2, "enabled": true, "key": "status", "type": "Number", "value": 1}, "userScore": {"comment": "用户得分", "customFlag": 2, "enabled": true, "key": "userScore", "type": "Number", "value": 1}, "standardScore": {"comment": "试卷标准分", "customFlag": 2, "enabled": true, "key": "standardScore", "type": "Number", "value": 1}, "paperScore": {"comment": "试卷成绩对象", "customFlag": 2, "enabled": true, "key": "paperScore", "type": "Object", "value": {}}}}, "success": {"comment": "是否成功", "customFlag": 2, "enabled": true, "key": "success", "type": "Boolean", "value": true}}}}, "tempId": "", "url": "/reader/paper/answer/submitUserPaperAnswer", "urlEncodedKeyValueListJson": "[]", "urlEncodedKeyValueListText": "", "urlParamsKeyValueListJson": "[]", "urlParamsKeyValueListText": ""}, "pmRequestId": "", "pmResponseId": "", "tempId": "api_com.unipus.digitalbook.controller.reader.ReaderPaperController.submitUserPaperAnswer", "type": 2}