{"activeGroup": "<PERSON><PERSON><PERSON>", "apiParamGroup": {}, "childList": [], "classDescription": "用户相关功能", "description": "", "directory": "/.fastRequest/collections/Root/DigitalBook/UserController", "domain": "http://localhost:8889/api", "enableEnv": "local,base", "enableProject": "DigitalBook", "filePath": "/.fastRequest/collections/Root/DigitalBook/UserController~getUserInfoById.rapi", "id": "api_com.unipus.digitalbook.controller.UserController.getUserInfoById", "mark": false, "module": "DigitalBook", "name": "获取用户角色信息", "paramGroup": {"binaryFilePath": "", "bodyKeyValueListJson": "", "bodyParamMap": {}, "classDescription": "用户相关功能", "className": "com.unipus.digitalbook.controller.UserController", "headerParamsKeyValueList": [], "jsonDocument": "", "method": "getUserInfoById", "methodDescription": "获取用户角色信息", "methodType": "GET", "multipartKeyValueListJson": "[]", "originUrl": "/user/getUserInfo", "pathParamsKeyValueListJson": "[]", "postScript": "", "postType": "json", "preScript": "", "returnDocument": "{\"code\":\"状态码\",\"message\":\"响应消息\",\"data\":{\"id\":\"用户ID\",\"ssoId\":\"SSO ID\",\"name\":\"姓名\",\"cellPhone\":\"手机号\",\"email\":\"邮箱地址\",\"gender\":\"性别，0:女, 1:男, 2:未知\",\"desc\":\"简介\",\"avatarUrl\":\"头像地址\",\"activeTime\":\"激活时间\"},\"success\":\"是否成功\"}", "returnTypeParamMap": {"return": {"customFlag": 2, "enabled": true, "key": "return", "type": "Object", "value": {"code": {"comment": "状态码", "customFlag": 2, "enabled": true, "key": "code", "type": "Number", "value": 1}, "message": {"comment": "响应消息", "customFlag": 2, "enabled": true, "key": "message", "type": "String", "value": "message_9y4il"}, "data": {"comment": "响应数据", "customFlag": 2, "enabled": true, "key": "data", "type": "Object", "value": {"id": {"comment": "用户ID", "customFlag": 2, "enabled": true, "key": "id", "type": "Number", "value": 123456}, "ssoId": {"comment": "SSO ID", "customFlag": 2, "enabled": true, "key": "ssoId", "type": "String", "value": "sso123456"}, "name": {"comment": "姓名", "customFlag": 2, "enabled": true, "key": "name", "type": "String", "value": "张三"}, "cellPhone": {"comment": "手机号", "customFlag": 2, "enabled": true, "key": "cellPhone", "type": "String", "value": "13800138000"}, "email": {"comment": "邮箱地址", "customFlag": 2, "enabled": true, "key": "email", "type": "String", "value": "<EMAIL>"}, "gender": {"comment": "性别，0:女, 1:男, 2:未知", "customFlag": 2, "enabled": true, "key": "gender", "type": "Number", "value": 1}, "desc": {"comment": "简介", "customFlag": 2, "enabled": true, "key": "desc", "type": "String", "value": "这是一个用户简介"}, "avatarUrl": {"comment": "头像地址", "customFlag": 2, "enabled": true, "key": "avatarUrl", "type": "String", "value": "https://example.com/avatar.jpg"}, "activeTime": {"comment": "激活时间", "customFlag": 2, "enabled": true, "key": "activeTime", "type": "String", "value": "2025-08-06 19:58:03"}}}, "success": {"comment": "是否成功", "customFlag": 2, "enabled": true, "key": "success", "type": "Boolean", "value": true}}}}, "tempId": "", "url": "/user/getUserInfo", "urlEncodedKeyValueListJson": "[]", "urlEncodedKeyValueListText": "", "urlParamsKeyValueListJson": "[{\"comment\":\"\",\"customFlag\":2,\"enabled\":true,\"key\":\"userId\",\"type\":\"Number\",\"value\":\"3\"}]", "urlParamsKeyValueListText": "userId=3"}, "pmRequestId": "", "pmResponseId": "", "tempId": "api_com.unipus.digitalbook.controller.UserController.getUserInfoById", "type": 2}