{"activeGroup": "<PERSON><PERSON><PERSON>", "apiParamGroup": {}, "childList": [], "classDescription": "用户相关功能", "description": "", "directory": "/.fastRequest/collections/Root/DigitalBook/UserController", "domain": "http://localhost:8889/api", "enableEnv": "local,base", "enableProject": "DigitalBook", "filePath": "/.fastRequest/collections/Root/DigitalBook/UserController~importByExcel.rapi", "id": "api_com.unipus.digitalbook.controller.UserController.importByExcel", "mark": false, "module": "DigitalBook", "name": "通过Excel，导入用户", "paramGroup": {"binaryFilePath": "", "bodyKeyValueListJson": "", "bodyParamMap": {}, "classDescription": "用户相关功能", "className": "com.unipus.digitalbook.controller.UserController", "headerParamsKeyValueList": [], "jsonDocument": "", "method": "importByExcel", "methodDescription": "通过Excel，导入用户", "methodType": "POST", "multipartKeyValueListJson": "[{\"comment\":\"\",\"customFlag\":2,\"enabled\":true,\"key\":\"orgId\",\"type\":\"Number\",\"value\":\"3\"},{\"comment\":\"Excel文件\",\"customFlag\":2,\"enabled\":true,\"key\":\"file\",\"type\":\"File\",\"value\":\"C:/Users/<USER>/Documents/工作计划日程表.xlsx\"}]", "originUrl": "/user/importByExcel", "pathParamsKeyValueListJson": "[]", "postScript": "", "postType": "json", "preScript": "", "returnDocument": "{\"code\":\"状态码\",\"message\":\"响应消息\",\"data\":{\"taskId\":\"No comment,Type =Number\"},\"success\":\"是否成功\"}", "returnTypeParamMap": {"return": {"customFlag": 2, "enabled": true, "key": "return", "type": "Object", "value": {"code": {"comment": "状态码", "customFlag": 2, "enabled": true, "key": "code", "type": "Number", "value": 1}, "message": {"comment": "响应消息", "customFlag": 2, "enabled": true, "key": "message", "type": "String", "value": "message_vb80e"}, "data": {"comment": "响应数据", "customFlag": 2, "enabled": true, "key": "data", "type": "Object", "value": {"taskId": {"comment": "", "customFlag": 2, "enabled": true, "key": "taskId", "type": "Number", "value": 1}}}, "success": {"comment": "是否成功", "customFlag": 2, "enabled": true, "key": "success", "type": "Boolean", "value": true}}}}, "tempId": "", "url": "/user/importByExcel", "urlEncodedKeyValueListJson": "[]", "urlEncodedKeyValueListText": "", "urlParamsKeyValueListJson": "[]", "urlParamsKeyValueListText": ""}, "pmRequestId": "", "pmResponseId": "", "tempId": "api_com.unipus.digitalbook.controller.UserController.importByExcel", "type": 2}