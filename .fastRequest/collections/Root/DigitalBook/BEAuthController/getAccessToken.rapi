{"activeGroup": "<PERSON><PERSON><PERSON>", "apiParamGroup": {}, "childList": [], "classDescription": "内部访问认证与权限管理模块", "description": "", "directory": "/.fastRequest/collections/Root/DigitalBook/BEAuthController", "domain": "http://localhost:8889/api", "enableEnv": "local,base", "enableProject": "DigitalBook", "filePath": "/.fastRequest/collections/Root/DigitalBook/BEAuthController~getAccessToken.rapi", "id": "api_com.unipus.digitalbook.controller.backend.BEAuthController.getAccessToken", "mark": false, "module": "DigitalBook", "name": "获取内部访问token", "paramGroup": {"binaryFilePath": "", "bodyKeyValueListJson": "{\n  \"openId\": \"030d8574e8b343e5bd7b30361df2e5cf\",\n  \"readerType\": 2,\n  \"dataPackage\": \"1053\",\n  \"appId\": 1,\n  \"envPartition\":  \"dev\",\n}", "bodyParamMap": {"param": {"customFlag": 2, "enabled": true, "key": "param", "type": "Object", "value": {"openId": {"comment": "openID", "customFlag": 2, "enabled": true, "key": "openId", "type": "String", "value": "openId_31rw3"}, "readerType": {"comment": "读者类型（1:学生/2:老师）", "customFlag": 2, "enabled": true, "key": "readerType", "type": "Number", "value": 1}, "dataPackage": {"comment": "用户数据包", "customFlag": 2, "enabled": true, "key": "dataPackage", "type": "String", "value": "dataPackage_jxz3z"}, "appId": {"comment": "客户端ID", "customFlag": 2, "enabled": true, "key": "appId", "type": "Number", "value": 1}, "envPartition": {"comment": "环境分区", "customFlag": 2, "enabled": true, "key": "envPartition", "type": "String", "value": "envPartition_y9hg7"}}}}, "classDescription": "内部访问认证与权限管理模块", "className": "com.unipus.digitalbook.controller.backend.BEAuthController", "headerParamsKeyValueList": [], "jsonDocument": "{\n  \"openId\": \"openID\",\n  \"readerType\": \"读者类型（1:学生/2:老师）\",\n  \"dataPackage\": \"用户数据包\",\n  \"appId\": \"客户端ID\",\n  \"envPartition\": \"环境分区\"\n}", "method": "getAccessToken", "methodDescription": "获取内部访问token", "methodType": "POST", "multipartKeyValueListJson": "[]", "originUrl": "/backend/auth/getAccessToken", "pathParamsKeyValueListJson": "[]", "postScript": "\nimport com.alibaba.fastjson.JSON\nJSON data = JSON.parseObject(rfr.response.body()).getJSONObject(\"data\")\nString token = data.getString(\"token\")\nrfr.environment.put(\"access_token\",\"Bearer ${token}\")\n\nconsole.print(rfr.environment.get(\"access_token\"))", "postType": "json", "preScript": "", "returnDocument": "{\"code\":\"状态码\",\"message\":\"响应消息\",\"data\":{\"token\":\"AccessToken\"},\"success\":\"是否成功\"}", "returnTypeParamMap": {"return": {"customFlag": 2, "enabled": true, "key": "return", "type": "Object", "value": {"code": {"comment": "状态码", "customFlag": 2, "enabled": true, "key": "code", "type": "Number", "value": 1}, "message": {"comment": "响应消息", "customFlag": 2, "enabled": true, "key": "message", "type": "String", "value": "message_q309t"}, "data": {"comment": "响应数据", "customFlag": 2, "enabled": true, "key": "data", "type": "Object", "value": {"token": {"comment": "AccessToken", "customFlag": 2, "enabled": true, "key": "token", "type": "String", "value": "token_jfnyw"}}}, "success": {"comment": "是否成功", "customFlag": 2, "enabled": true, "key": "success", "type": "Boolean", "value": true}}}}, "tempId": "", "url": "/backend/auth/getAccessToken", "urlEncodedKeyValueListJson": "[]", "urlEncodedKeyValueListText": "", "urlParamsKeyValueListJson": "[]", "urlParamsKeyValueListText": ""}, "pmRequestId": "", "pmResponseId": "", "tempId": "api_com.unipus.digitalbook.controller.backend.BEAuthController.getAccessToken", "type": 2}